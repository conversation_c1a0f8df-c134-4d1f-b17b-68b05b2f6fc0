<component name="ArtifactManager">
  <artifact type="jar" name="Activo:jar">
    <output-path>$PROJECT_DIR$/out/artifacts/Activo_jar</output-path>
    <root id="archive" name="Activo.jar">
      <element id="module-output" name="Activo" />
      <element id="extracted-dir" path="$MAVEN_REPOSITORY$/javax/activation/activation/1.1/activation-1.1.jar" path-in-jar="/" />
      <element id="extracted-dir" path="$MAVEN_REPOSITORY$/com/itextpdf/itextpdf/5.5.13.3/itextpdf-5.5.13.3.jar" path-in-jar="/" />
      <element id="extracted-dir" path="$MAVEN_REPOSITORY$/com/itextpdf/tool/xmlworker/5.5.13.3/xmlworker-5.5.13.3.jar" path-in-jar="/" />
      <element id="extracted-dir" path="$MAVEN_REPOSITORY$/com/sun/mail/javax.mail/1.6.2/javax.mail-1.6.2.jar" path-in-jar="/" />
      <element id="extracted-dir" path="$MAVEN_REPOSITORY$/net/sf/saxon/saxon/8.7/saxon-8.7.jar" path-in-jar="/" />
      <element id="extracted-dir" path="$MAVEN_REPOSITORY$/com/microsoft/sqlserver/mssql-jdbc/12.2.0.jre11/mssql-jdbc-12.2.0.jre11.jar" path-in-jar="/" />
    </root>
  </artifact>
</component>