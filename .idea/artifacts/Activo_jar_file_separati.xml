<component name="ArtifactManager">
  <artifact name="Activo:jar file separati">
    <output-path>$PROJECT_DIR$/out/artifacts/Activo_jar_file_separati</output-path>
    <root id="root">
      <element id="archive" name="Activo.jar">
        <element id="module-output" name="Activo" />
      </element>
      <element id="library" level="project" name="Maven: javax.activation:activation:1.1" />
      <element id="library" level="project" name="Maven: com.itextpdf:itextpdf:5.5.13.3" />
      <element id="library" level="project" name="Maven: com.sun.mail:javax.mail:1.6.2" />
      <element id="library" level="project" name="Maven: net.sf.saxon:saxon:8.7" />
      <element id="library" level="project" name="Maven: com.itextpdf.tool:xmlworker:5.5.13.3" />
      <element id="library" level="project" name="Maven: com.microsoft.sqlserver:mssql-jdbc:12.2.0.jre11" />
    </root>
  </artifact>
</component>