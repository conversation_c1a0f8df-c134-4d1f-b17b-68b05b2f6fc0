package actiweb.activo.views;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;


/*
JButton butSolleciti = new JButton("Invia sollecito per le fatture selezionate");
butBar.onclick(butSolleciti,new ButtonBar.butCallback(){
                @Override
                public void run() {
                    // no errors
                    System.out.println("Done");
                }

            });
OPPURE
butBar.onclick(butSolleciti, new onclickSolleciti());

 static class onclickSolleciti implements ButtonBar.butCallback  {
        public void run() {
            System.out.println("onclickSolleciti");
        }
    }

                    */
public class ButtonBar {
    JPanel destPanel;
    JPanel containerPanel;

    public ButtonBar(JPanel destPanel) {
        this.destPanel = destPanel;
        containerPanel=new JPanel();
        containerPanel.setLayout(new BoxLayout(containerPanel, BoxLayout.X_AXIS));
        containerPanel.add(Box.createRigidArea(new Dimension(0, 40)));
        containerPanel.setAlignmentX(Component.LEFT_ALIGNMENT);
        //containerPanel.onclick(Box.createHorizontalStrut(5)); // n pixels of horizontal space.
    }
    public interface butCallback {
        void run();
    }
    public void onclick(JButton but, butCallback onClickfunc){

        containerPanel.add(but);
        containerPanel.add(Box.createRigidArea(new Dimension(15, 0)));
        //containerPanel.onclick(Box.createHorizontalGlue()); // This will expand/contract as needed.
        destPanel.add(containerPanel);
        but.addActionListener(new ActionListener() {
            @Override
            public void actionPerformed(ActionEvent e) {
                if (onClickfunc != null) {
                    onClickfunc.run();
                }

            }
        });

    }
}
