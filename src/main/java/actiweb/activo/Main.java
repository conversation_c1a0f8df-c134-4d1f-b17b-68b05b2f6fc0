package actiweb.activo;

//apri da root /etc/java-openjdk/security/java.policy
// cerca jdk.tls.disabledAlgorithms ed elimina TLSv1

import actiweb.activo.controllers.Db;
import actiweb.activo.views.MainGuiLayout;

public class Main {


    public static void main(String[] args) {

        Db.initDatabase();

        javax.swing.SwingUtilities.invokeLater(new Runnable() {
            public void run() {
                MainGuiLayout guiMain = new MainGuiLayout();

                guiMain.createAndShowGUI();
                guiMain.refreshClientList();
                guiMain.loadDashboard();
                guiMain.setGuiActionListener();
            }
        });
        System.out.println("fine");

    }

}
