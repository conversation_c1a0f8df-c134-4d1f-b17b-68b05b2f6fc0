package actiweb.activo;


//apri da root /etc/java-openjdk/security/java.policy
// cerca jdk.tls.disabledAlgorithms ed elimina TLSv1

import actiweb.activo.controllers.Db;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class TestConnect {


    public static void main(String[] args) throws SQLException {


        Db.initDatabase();
        //Statement statement = null;
        // if (activoDb.getDbConn()!=null) {
        // if (Db.getDbConn()!=null) {
        System.out.println("assasa");

/*        try (ResultSet rs = Db.query()) {
            while (rs.next()) {
                System.out.println(rs.getString("COLUMN_NAME")); //stampa le colonne della tabella
            }
        }
        catch (SQLException e) {
            throw new RuntimeException(e);
        }*/

        try (Statement statement = Db.getDbConn().createStatement()) {

            String COLONNE_NELLA_TABELLA = """
                    select *
                    from INFORMATION_SCHEMA.COLUMNS
                    where TABLE_NAME='PrimaNotaSocieta'""";

            ResultSet rs = statement.executeQuery(COLONNE_NELLA_TABELLA);

            while (rs.next()) {
                System.out.println(rs.getString("COLUMN_NAME")); //stampa le colonne della tabella
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }


        try (Statement statement = Db.getDbConn().createStatement()) {
            //try (Connection con = Db.getDbConn(); Statement statement = con.createStatement();) {

            String COLONNE_NELLA_TABELLA = """
                    select *
                    from INFORMATION_SCHEMA.COLUMNS
                    where TABLE_NAME='PrimaNotaSocieta'""";

            ResultSet rs = statement.executeQuery(COLONNE_NELLA_TABELLA);

            while (rs.next()) {
                System.out.println(rs.getString("COLUMN_NAME")); //stampa le colonne della tabella
            }
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

        // }


      /*  //da qui testing classe Db
        Connection dbConn = Db.getDbConnection();
        System.out.println("qui");
        Statement statement = null;
        if (dbConn != null) {
            System.out.println("assasa");
            statement = dbConn.createStatement();
            String COLONNE_NELLA_TABELLA = """
                    select *
                    from INFORMATION_SCHEMA.COLUMNS
                    where TABLE_NAME='PrimaNotaSocieta'""";

            ResultSet rs = statement.executeQuery(COLONNE_NELLA_TABELLA);

            while (rs.next()) {

                System.out.println(rs.getString("COLUMN_NAME")); //stampa le colonne della tabella
                //ResultSetMetaData rsmd = rs.getMetaData();
                //String name = rsmd.getColumnName(4);
                //System.out.println(name);
            }
        }
        System.out.println(dbConn);
        if (dbConn != null) {
            dbConn.close();
        }
        System.out.println(dbConn);
        if (dbConn != null) {
            statement = dbConn.createStatement();
            System.out.println("sss");
        }

        //da qui fine testing classe Db
*/

       /* DA QUI TEST NORMALE

        // Create a variable for the connection string.
        String connectionUrl = "jdbc:sqlserver://195.130.247.81:1433;encrypt=false;databaseName=LedbActi;user=Leuserdbd;password=********";

        try (Connection con = DriverManager.getConnection(connectionUrl); Statement stmt = con.createStatement();) {
            System.out.println("connesso");

            String SQL = """
                    SELECT
                      name,
                      crdate
                    FROM
                      SYSOBJECTS
                    WHERE
                      xtype = 'U';""";

            String COLONNE_NELLA_TABELLA = """
                    select *
                    from INFORMATION_SCHEMA.COLUMNS
                    where TABLE_NAME='ElencoScarico'""";

            SQL = COLONNE_NELLA_TABELLA;

            ResultSet rs = stmt.executeQuery(SQL);

            // Iterate through the data in the result set and display it.

            while (rs.next()) {

                System.out.println(rs.getString("COLUMN_NAME")); //stampa le colonne della tabella
                //ResultSetMetaData rsmd = rs.getMetaData();
                //String name = rsmd.getColumnName(4);
                //System.out.println(name);
            }
        }
        // Handle any errors that may have occurred.
        catch (SQLException e) {
            e.printStackTrace();
        }

       */

    }
}
