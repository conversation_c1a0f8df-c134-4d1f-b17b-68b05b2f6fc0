package actiweb.activo.controllers;

import actiweb.activo.models.ClienteModel;
import actiweb.activo.models.FatturaModel;
import actiweb.activo.models.SollecitoModel;
import actiweb.activo.views.ButtonBar;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.*;

import static actiweb.activo.controllers.EmailSender.sendEmail;
import static actiweb.activo.controllers.Utils.*;


public class Solleciti {

    public static String replacePlaceholders(String text, Map<String, String> placeholders) {
        if (text == null || text.isEmpty() || placeholders == null || placeholders.isEmpty()) {
            return text;
        }
        for (String placeholder : placeholders.keySet()) {
            text = text.replace("{" + placeholder + "}", placeholders.get(placeholder));
        }
        return text;
    }

    public static HashMap<String, ArrayList<FatturaModel>> extractSollecitiDaListaFatture(JTable table) {
        int[] selectedRows = table.getSelectedRows();
        HashMap<String, ArrayList<FatturaModel>> raggruppamenti = new HashMap<>();
        System.out.println("extractSollecitiDaListaFatture ");
        //panelSolleciti.removeAll();
        for (int selectedRow : selectedRows) {

/*            Object idFattura = table.getValueAt(selectedRow, 0);
            //Object ragSoc = table.getValueAt(selectedRow, 1);
            Object fatNum = table.getValueAt(selectedRow, 1);
            Object dataFatObj = table.getValueAt(selectedRow, 2);
            Object importoFat = table.getValueAt(selectedRow, 4);
            Object idCliente = table.getValueAt(selectedRow, 5);
            String idClienteStr = idCliente.toString();
            Object PrezzoPagato = table.getValueAt(selectedRow, 3);   */
            int colIndex = 0;
            colIndex = table.getColumn(Utils.getColName("idFattura")).getModelIndex(); //idFattura
            Object idFattura = table.getValueAt(selectedRow, colIndex);

            colIndex = table.getColumn(Utils.getColName("numFattura")).getModelIndex();
            Object fatNum = table.getValueAt(selectedRow, colIndex);

            colIndex = table.getColumn(Utils.getColName("data")).getModelIndex();
            Object dataFatObj = table.getValueAt(selectedRow, colIndex);
            System.out.println("dataFatObj " + dataFatObj.toString());

            colIndex = table.getColumn(Utils.getColName("totale")).getModelIndex();
            Object importoFat = table.getValueAt(selectedRow, colIndex);

            colIndex = table.getColumn(Utils.getColName("totalePagato")).getModelIndex();
            Object PrezzoPagato = table.getValueAt(selectedRow, colIndex);

            colIndex = table.getColumn(Utils.getColName("idCliente")).getModelIndex();
            Object idCliente = table.getValueAt(selectedRow, colIndex);
            String idClienteStr = idCliente.toString();


            //double pPagato = Double.parseDouble(PrezzoPagato.toString());
            double pPagato = Utils.parseDecimal(PrezzoPagato.toString());

            if (pPagato == 0) {
                FatturaModel ft = new FatturaModel();
                ft.setIdFattura(idFattura.toString());
                ft.setNumFattura(fatNum.toString());
                ft.setIdCliente(idCliente.toString());
/*                DateFormat df = new SimpleDateFormat("dd/MM/yyyy");
                try {
                    ft.setDataFattura(df.parse(dataFatObj.toString()));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }*/

                // ft.setDataFattura((Date) dataFatObj);
                System.out.println("setting data " + dataFatObj);
                // System.out.println("parsed data " + Utils.parseDate(dataFatObj.toString()));
                // ft.setDataFattura(Utils.parseDate(dataFatObj.toString()));

                Date myData = Utils.convertStringToDate(dataFatObj.toString());
                System.out.println(dataFatObj + " -> stringToDataFormatted = " + myData);
                ft.setDataFattura(myData);

                ft.setPrezzoTotaleFattura(Utils.parseDecimal(importoFat.toString()));

                if (raggruppamenti.containsKey(idClienteStr)) {
                    // Aggiunta dell'elemento alla lista corrispondente alla chiave
                    raggruppamenti.get(idClienteStr).add(ft);
                } else {
                    // Creazione di una nuova lista con l'elemento e aggiunta al map con la chiave
                    ArrayList<FatturaModel> lista = new ArrayList<>();
                    lista.add(ft);
                    raggruppamenti.put(idClienteStr, lista);
                }
            } else {
                String err = "La fattura " + fatNum + " selezionata non è stata inclusa perchè è pagata!";
                //System.out.println("fattura "+fatNum+" non considerata perchè è pagata!");
                JOptionPane.showMessageDialog(null, err, "Attenzione", JOptionPane.INFORMATION_MESSAGE);

            }

            // System.out.println(idFattura + " Selected Item: " + ragSoc + " Fat:" + fatNum + " idCliente: " + idCliente);
        }

        return raggruppamenti;
    }

    public static HashMap<String, ArrayList<FatturaModel>> extractSollecitiDaNonPagate(JTable table) {
        int[] selectedRows = table.getSelectedRows();
        HashMap<String, ArrayList<FatturaModel>> raggruppamenti = new HashMap<>();

        //panelSolleciti.removeAll();
        for (int selectedRow : selectedRows) {
            Object idFattura = table.getValueAt(selectedRow, 0);
            Object ragSoc = table.getValueAt(selectedRow, 1);
            Object fatNum = table.getValueAt(selectedRow, 2);
            Object dataFatObj = table.getValueAt(selectedRow, 3);
            Object importoFat = table.getValueAt(selectedRow, 4);
            Object idCliente = table.getValueAt(selectedRow, 5);
            String idClienteStr = idCliente.toString();

            FatturaModel ft = new FatturaModel();
            ft.setIdFattura(idFattura.toString());
            ft.setNumFattura(fatNum.toString());
            ft.setIdCliente(idCliente.toString());
            ft.setDataFattura((Date) dataFatObj);
            ft.setPrezzoTotaleFattura(Double.parseDouble(importoFat.toString()));


            if (raggruppamenti.containsKey(idClienteStr)) {
                // Aggiunta dell'elemento alla lista corrispondente alla chiave
                raggruppamenti.get(idClienteStr).add(ft);
            } else {
                // Creazione di una nuova lista con l'elemento e aggiunta al map con la chiave
                ArrayList<FatturaModel> lista = new ArrayList<>();
                lista.add(ft);
                raggruppamenti.put(idClienteStr, lista);
            }
            // System.out.println(idFattura + " Selected Item: " + ragSoc + " Fat:" + fatNum + " idCliente: " + idCliente);
        }

        return raggruppamenti;
    }

    public static void clickSollecitiBut(HashMap<String, ArrayList<FatturaModel>> raggruppamenti) {

        if (raggruppamenti.isEmpty()) {
            System.out.println("niente in raggruppamenti, esco");
            return;
        }

        SollecitoModel.TipoDiEmail tipoMailDaInviare = SollecitoModel.TipoDiEmail.SOLLECITO;
        String mailTemplate = mailTemplateSollecito();

        tipoMailDaInviare = popupPerTipoMailDaInviare();
        //System.out.println("tipoMailDaInviare = " + tipoMailDaInviare);

        String annoFat = null;
        String dataFat = null;

        // Stampa dei risultati
        Set<String> ragKeys = raggruppamenti.keySet();
        String fatNonPag = "Fattura {numFat}/{annoFat} del {dataFat} importo {importoFat}€ ";

        for (String rk : ragKeys) {
            //System.out.println("Cliente id=" + rk);
            ArrayList<FatturaModel> ftList = raggruppamenti.get(rk);
            StringBuffer listaFattPerCortesia = new StringBuffer();
            StringBuffer listaFatt = new StringBuffer();
            StringBuffer causale = new StringBuffer();
            ArrayList<String> listaIdFatture = new ArrayList<>();
            ArrayList<String> listaFatture = new ArrayList<>();
            //listaFatt.append("\n");
            for (FatturaModel fat : ftList) {
                // Date date = Utils.parseDate(String.valueOf(fat.getDataFattura()));
                dataFat = Utils.convertDateToString(fat.getDataFattura());
                //dataFat = Utils.formatDataToString(String.valueOf(fat.getDataFattura()));
                annoFat = Utils.convertDateToString(fat.getDataFattura(), "", "yy");
                //annoFat = dataFat;

                listaIdFatture.add(fat.getIdFattura());
                listaFatture.add(fat.getNumFattura() + "/" + annoFat);


                Map<String, String> placeholders = new HashMap<>();
                placeholders.put("numFat", fat.getNumFattura());
                placeholders.put("annoFat", annoFat);
                placeholders.put("dataFat", dataFat);
                //placeholders.put("importoFat", String.valueOf(fat.getPrezzoTotaleFattura()));
                placeholders.put("importoFat", Utils.formatMoney(fat.getPrezzoTotaleFattura()));


                String fatturaDaPagare = replacePlaceholders(fatNonPag, placeholders);

                if (sonoPassatiAlmenoGiorni(fat.getDataFattura(), 31)) {
                    fatturaDaPagare = fatturaDaPagare + " (SCADUTA) ";
                }

                listaFatt.append(fatturaDaPagare);
                listaFatt.append("\n");
                causale.append("FT.").append(fat.getNumFattura()).append("/").append(annoFat).append(",");
                listaFattPerCortesia.append("Fattura ").append(fat.getNumFattura()).append("/").append(annoFat);
                listaFattPerCortesia.append(" del ").append(dataFat).append(" + ");

            }
            //listaFatt.append("\n");
            int virg = causale.lastIndexOf(",");
            causale.deleteCharAt(virg);
            int cr = listaFatt.lastIndexOf("\n");
            listaFatt.deleteCharAt(cr);
            int piu = listaFattPerCortesia.lastIndexOf("+");
            listaFattPerCortesia.deleteCharAt(piu);

            Map<String, String> mailPlaceholders = new HashMap<>();
            mailPlaceholders.put("listaFatture", String.valueOf(listaFatt));
            mailPlaceholders.put("causale", String.valueOf(causale));


            String soggetto = "Sollecito di Pagamento Fatture " + causale;
            if (tipoMailDaInviare == SollecitoModel.TipoDiEmail.CORTESIA) {
                mailTemplate = mailTemplateCortesia();
                soggetto = listaFattPerCortesia.toString();
                LocalDate today = LocalDate.now();
                // Aggiungi 30 giorni alla data odierna
                LocalDate scData = today.plusDays(30);
                String scadenza = Utils.changeDateStrFormat(scData.toString(), "yyyy-MM-dd", "dd/MM/yyyy");
                mailPlaceholders.put("scadenza", scadenza);
            }

            String testoMail = replacePlaceholders(mailTemplate, mailPlaceholders);

            //System.out.println(testoMail);
            ClienteModel tempCliente = null;
            String mailcliente = "";
            String ragSoc = "";
            try {
                tempCliente = Clienti.getClienteData(rk);
                assert tempCliente != null;
                mailcliente = tempCliente.getEmail();
                ragSoc = tempCliente.getRagioneSociale();
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }

            SollecitoModel newSollecito;
            newSollecito = new SollecitoModel(rk, listaIdFatture, listaFatture, String.valueOf(listaFatt), ragSoc);
            newSollecito.setTipoDiComunicazione(tipoMailDaInviare);

            //Sollecito2 newsoc= new Sollecito2(rk,listaIdFatture,String.valueOf(listaFatt),ragSoc);
            EmailSender.mailMsg mes;
            mes = new EmailSender.mailMsg(mailcliente, "<EMAIL>,<EMAIL>,<EMAIL>", soggetto, testoMail);
            createMailWindow(mes, newSollecito);
        }


    }

    private static SollecitoModel.TipoDiEmail popupPerTipoMailDaInviare() {
        SollecitoModel.TipoDiEmail tipoMailDaInviare = SollecitoModel.TipoDiEmail.SOLLECITO;
        JRadioButton radioSollecito = new JRadioButton(SollecitoModel.descrTipoComunicazione(SollecitoModel.TipoDiEmail.SOLLECITO));
        radioSollecito.setSelected(true);
        JRadioButton radioCortesia = new JRadioButton(SollecitoModel.descrTipoComunicazione(SollecitoModel.TipoDiEmail.CORTESIA));
        ButtonGroup buttonGroup = new ButtonGroup();
        buttonGroup.add(radioSollecito);
        buttonGroup.add(radioCortesia);

        JPanel tipoMailPanel = new JPanel();
        tipoMailPanel.setLayout(new BoxLayout(tipoMailPanel, BoxLayout.Y_AXIS));
        tipoMailPanel.add(radioSollecito);
        tipoMailPanel.add(radioCortesia);

        Object[] opzioniTipoMail = {tipoMailPanel, "Continua"};
        String popupMes = "<html><center><h3>Seleziona il tipo di email da inviare al cliente</h3><hr></center></html>";
        JOptionPane.showOptionDialog(null, popupMes, "Tipo di comunicazione al cliente", JOptionPane.OK_CANCEL_OPTION, JOptionPane.QUESTION_MESSAGE, null, opzioniTipoMail, null);

        if (radioCortesia.isSelected()) {
            tipoMailDaInviare = SollecitoModel.TipoDiEmail.CORTESIA;
        }
        return tipoMailDaInviare;
    }

    public static void createMailWindow(EmailSender.mailMsg emailMes, SollecitoModel Sol) {

        JFrame frame = new JFrame("Invio mail al cliente (" + Sol.descrTipoComunicazioneCorrente() + ")");
        frame.setSize(1250, 750);
        frame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        JPanel firstRowPanel = new JPanel();
        firstRowPanel.setLayout(new BoxLayout(firstRowPanel, BoxLayout.Y_AXIS));
        JLabel emailLabel = new JLabel("Email destinatario (possono essere più mail separate da virgola)");
        JTextField emailField = new JTextField(40);
        emailField.setText(emailMes.getToEmail());
        emailLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        emailField.setAlignmentX(Component.LEFT_ALIGNMENT);

        JLabel bccEmailLabel = new JLabel("Email destinatario nascosto BCC (possono essere più mail separate da virgola)");
        JTextField bccEemailField = new JTextField(40);
        if (emailMes.isBccMailset()) {
            bccEemailField.setText(emailMes.getToBccEmail());
        }
        bccEmailLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        bccEemailField.setAlignmentX(Component.LEFT_ALIGNMENT);

        JLabel soggettoLabel = new JLabel("Soggetto");
        JTextField soggettoField = new JTextField(40);
        soggettoField.setText(emailMes.getSubject());
        soggettoLabel.setAlignmentX(Component.LEFT_ALIGNMENT);
        soggettoField.setAlignmentX(Component.LEFT_ALIGNMENT);

        firstRowPanel.add(emailLabel);
        firstRowPanel.add(emailField);
        firstRowPanel.add(bccEmailLabel);
        firstRowPanel.add(bccEemailField);
        firstRowPanel.add(soggettoLabel);
        firstRowPanel.add(soggettoField);

        firstRowPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // Create the first row
        frame.add(firstRowPanel, BorderLayout.NORTH);

        // Create the second row
        JPanel secondRowPanel = new JPanel();
        secondRowPanel.setLayout(new BoxLayout(secondRowPanel, BoxLayout.Y_AXIS));
        secondRowPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));


        JLabel emailTesto = new JLabel("Testo Email");

        JTextArea textarea = new JTextArea();
        textarea.setText(emailMes.getBody());

        emailTesto.setAlignmentX(Component.CENTER_ALIGNMENT);
        textarea.setAlignmentX(Component.LEFT_ALIGNMENT);

        secondRowPanel.add(emailTesto);
        secondRowPanel.add(textarea);

        JScrollPane scrollPane = new JScrollPane(textarea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS); // Abilita la barra di scorrimento verticale
        secondRowPanel.add(scrollPane);

        frame.add(secondRowPanel, BorderLayout.CENTER);

        JPanel storiaPanel = new JPanel();
        storiaPanel.setLayout(new BoxLayout(storiaPanel, BoxLayout.Y_AXIS));
        storiaPanel.setBorder(BorderFactory.createEmptyBorder(10, 5, 10, 15));
        JLabel storiaLabel = new JLabel("Storia solleciti");
        storiaLabel.setAlignmentX(Component.LEFT_ALIGNMENT);

        int[] colToHide = {0, 4, 5, 6};
        ArrayList<SollecitoModel> listaSolleciti = SollecitoModel.caricaSollecitiDalDb(Sol.idCliente);
        JPanel sollecitiPane = Solleciti.BuildStoricoSollecitiTableGui(listaSolleciti, colToHide);

        //update degli indirizzi di posta del destinatario in base ai solleciti inviati in passato
        ArrayList<String> listaMailDest = new ArrayList<>();
        listaMailDest.add(emailMes.getToEmail());

        for (SollecitoModel sol : listaSolleciti) {
            for (String mail : sol.listaMailDestinatario) {
                if ((!(listaMailDest.contains(mail))) && (!mail.isBlank())) {
                    listaMailDest.add(mail);
                }
            }
        }
        String listaMailDestString = String.join(",", listaMailDest);
        emailField.setText(listaMailDestString);

        //storiaPanel.add(storiaSolleciti);
        storiaPanel.add(storiaLabel);
        storiaPanel.add(sollecitiPane);
        storiaPanel.setVisible(false);

        frame.add(storiaPanel, BorderLayout.EAST);

        // Create the third row
        JPanel thirdRowPanel = new JPanel();
        thirdRowPanel.setLayout(new BoxLayout(thirdRowPanel, BoxLayout.X_AXIS));

        JButton butInviaSollecito = new JButton("Invia >");
        JButton butAnnulla = new JButton("< Chiudi");
        JButton butStoria = new JButton("Storia Solleciti");

        thirdRowPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
       /* butInviaSollecito.setAlignmentX(Component.RIGHT_ALIGNMENT);
        butAnnulla.setAlignmentX(Component.LEFT_ALIGNMENT);
        butStoria.setAlignmentX(Component.CENTER_ALIGNMENT);*/

        thirdRowPanel.setAlignmentX(Component.CENTER_ALIGNMENT);
        ButtonBar butBar = new ButtonBar(thirdRowPanel);
        butBar.onclick(butAnnulla, frame::dispose);
        thirdRowPanel.add(Box.createHorizontalGlue());

        butBar.onclick(butStoria, () -> {
            storiaPanel.setVisible(!storiaPanel.isVisible());
        });

        thirdRowPanel.add(Box.createHorizontalGlue());

        //butBar.onclick(butInviaSollecito, Solleciti::onclickInviaSollecito(Sol));
        butBar.onclick(butInviaSollecito, () -> {
            emailMes.setToEmail(emailField.getText());
            emailMes.setToBccEmail(bccEemailField.getText());
            emailMes.setSubject(soggettoField.getText());
            emailMes.setBody(textarea.getText());
            onclickInviaSollecito(emailMes, Sol);
        });

        frame.add(thirdRowPanel, BorderLayout.PAGE_END);
        frame.setLocationRelativeTo(null); // Posiziona il JFrame al centro dello schermo
        frame.setVisible(true);

    }

    public static void onclickInviaSollecito(EmailSender.mailMsg emailMes, SollecitoModel Sollecito) {

        if (!emailMes.getToEmail().contains("@")) {
            JOptionPane.showMessageDialog(null, emailMes.getToEmail() + " NON è una mail valida!", "Sollecito", JOptionPane.INFORMATION_MESSAGE);
            return;
        }

        StringBuffer confirmMes = new StringBuffer();
        confirmMes.append("Invio comunicazione per: \n\n");
        confirmMes.append(Sollecito.testoSollecito);
        confirmMes.append("\n\nCliente: ");
        confirmMes.append(Sollecito.getRagioneSociale());
        confirmMes.append("\n\nDestinatari: ");
        confirmMes.append(emailMes.getToEmail());
        if (emailMes.isBccMailset()) {
            confirmMes.append("\nDestinatari nascosti: ");
            confirmMes.append(emailMes.getToBccEmail());
        }
        confirmMes.append("\n\n");
        //+SollecitoModel.testoSollecito;
        // Mostra un alert box di conferma
        int choice = JOptionPane.showConfirmDialog(null, confirmMes, "Sei sicuro? (" + Sollecito.descrTipoComunicazioneCorrente() + ")", JOptionPane.YES_NO_OPTION);

        // Verifica la scelta dell'utente
        if (choice == JOptionPane.NO_OPTION) {
            System.out.println("Hai annullato.");
        } else {
            System.out.println("Hai confermato.");

            JDialog loading = alert("<b><h2>Generazione PDF fatture e invio Email in corso..</h2></b><hr><br>" + confirmMes);

            Thread t = new Thread(() -> {
                //aggiungo le fatture pdf in allegato
                StringBuilder resultMessage = new StringBuilder(" ");
                resultMessage.append("\nFile pdf allegati:");
                for (String idFat : Sollecito.listaIdFatture) {
                    String singlePdf = "";
                    //creo il pdf della fattura e ricevo il percorso del file
                    singlePdf = Fatture.creaPdfDaXml(idFat);
                    if (!singlePdf.isEmpty()) {
                        System.out.println(singlePdf);
                        //aggiungo il percorso del file pdf creato alla lista dei file da allegare
                        emailMes.addAttachmentFile(singlePdf);
                        resultMessage.append("\n");
                        resultMessage.append(singlePdf);
                    }
                }
                resultMessage.append("\n----------------------\n");
                EmailSender.emailResult sendEmailResult = new EmailSender.emailResult();
                sendEmailResult = sendEmail(emailMes);
                if (!sendEmailResult.retResult) {
                    JOptionPane.showMessageDialog(null, sendEmailResult.retStr, "Errore", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                resultMessage.append(sendEmailResult.retStr);

                resultMessage.append("\n----------------------");
                Sollecito.listaMailDestinatario.add(emailMes.getToEmail());
                int res = Sollecito.salvaSollecitoNelDb();
                //int res = SollecitoModel.salvaSollecitoNelDb(Sollecito);
                if (res > 0) {
                    resultMessage.append("\nSollecito Salvato correttamente nel database.\n");
                } else {
                    resultMessage.append("\nERRORE salvataggio Sollecito \n");
                }
                JOptionPane.showMessageDialog(null, resultMessage, "Sollecito", JOptionPane.INFORMATION_MESSAGE);

                loading.setVisible(false); // Nasconde il dialog
                loading.dispose(); // Distrugge il dialog

            });
            t.start(); // Avvia il thread


        }
    }

    public static String mailTemplateSollecito() {
        return """
                Gentile Cliente, in base ai nostri controlli contabili non ci risultano ancora saldate le fatture che seguono.
                La invitiamo a provvedere al pagamento quanto prima possibile, al fine di evitare interruzioni del servizio.
                                                
                {listaFatture}
                                
                Può saldare l'importo tramite bonifico bancario utilizzando le coordinate:
                                
                IBAN: IT 25 I 03268 41630 052844338970
                BANCA SELLA - PUTIGNANO (BA)
                Intestato alla  ACTIWEB S.C. A R.L.
                Causale "Saldo {causale}"
                                
                Ai fini fiscali le corrispondenti Fatture Elettroniche sono disponibili in area riservata presso il sito dell'Agenzia delle Entrate.
                                
                In caso vogliate contattarci:
                Orari ufficio dal lunedì al venerdì: ore 9:00 – 13:00, ore 16:00 – 19:30
                Tel. 080 4964 306
                Cell. 392 15 75 502
                                
                Restiamo a sua disposizione per qualsiasi chiarimento.\s
                                
                Cordiali saluti.
                                
                                
                *****************************************************
                ActiWeb S. c. a r. l.
                Sede legale: Via Calatafimi n° 87
                70043 Monopoli (BA)
                Sede operativa: Via Bainsizza n° 18/B
                70013 Castellana Grotte (BA)
                P.IVA: 05678210724
                Tel. +39 080 4964306
                Fax +39 080 2142633
                E-mail: <EMAIL>
                E-mail Certificata: <EMAIL>
                PROVIDER/MAINTAINER: ACTIWEB-REG
                ****************************************************
                               
                """;
    }

    public static String mailTemplateCortesia() {
        return """
                Gentile cliente,
                in allegato trova copia di cortesia in versione PDF delle fatture seguenti:
                    
                {listaFatture}
                    
                Può saldare l'importo entro il {scadenza} tramite bonifico bancario utilizzando le coordinate:
                    
                IBAN: IT 25 I 03268 41630 052844338970
                BANCA SELLA - PUTIGNANO (BA)
                Intestato alla  ACTIWEB S.C. A R.L.
                Causale "Saldo {causale}"
                    
                Ai fini fiscali, l'originale di quanto allegato è stato emesso in formato elettronico a norma di legge ed è disponibile nell'area riservata del sito dell'Agenzia delle Entrate.
                    
                In caso vogliate contattarci:
                Orari ufficio dal lunedì al venerdì: ore 9:00 – 13:00, ore 16:00 – 19:30
                Tel. 080 4964 306
                Cell. 392 15 75 502
                    
                Restiamo a sua disposizione per qualsiasi chiarimento.
                    
                Cordiali saluti.
                    
                    
                *****************************************************
                ActiWeb S. c. a r. l.
                Sede legale: Via Calatafimi n° 87
                70043 Monopoli (BA)
                Sede operativa: Via Bainsizza n° 18/B
                70013 Castellana Grotte (BA)
                P.IVA: 05678210724
                Tel. +39 080 4964306
                Fax +39 080 2142633
                E-mail: <EMAIL>
                E-mail Certificata: <EMAIL>
                PROVIDER/MAINTAINER: ACTIWEB-REG
                ****************************************************
                               
                """;
    }

    public static JPanel BuildStoricoSollecitiTableGui(ArrayList<SollecitoModel> listaSolleciti, int[] columnToHide) {


        String[] columnNames = {"Cliente", "Fatture", "Testo", "Data invio", "Id Cliente", "idSollecito", "listaIdFatture", "Tipo"};
        // Object[][] data = {{"Smith", "Snowboarding", "ciao"},{"Smith", "Snowboarding", "ciao"}};
        Object[][] data = new Object[listaSolleciti.size()][8];

        //JTable table = new JTable(data, columnNames);
        DefaultTableModel tm = new DefaultTableModel(data, columnNames);
        JTable table = new JTable(tm);

        JScrollPane scrollPaneForTable = new JScrollPane(table);
        table.setFillsViewportHeight(true);
        table.setAutoCreateRowSorter(true);

        int row = 0;
        for (SollecitoModel sol : listaSolleciti) {
            //System.out.println(sol.getNumFattura());
            table.setValueAt(sol.getRagioneSociale(), row, 0);
            table.setValueAt(sol.getListaFattureString(), row, 1);
            table.setValueAt(sol.testoSollecito, row, 2);
            table.setValueAt(sol.dataSollecito, row, 3);
            table.setValueAt(sol.idCliente, row, 4);
            table.setValueAt(sol.idSollecito, row, 5);
            table.setValueAt(sol.getListaIdFattureString(), row, 6);
            table.setValueAt(sol.getTipoDiComunicazione(), row, 7);
            //table.setValueAt(selCliente.getIdCliente(), row, 5);
            row++;
        }

        for (int i = 0; i <= columnNames.length; i++) {
            int index = Arrays.binarySearch(columnToHide, i);

            if (index >= 0) {
                table.getColumnModel().getColumn(i).setMinWidth(0);
                table.getColumnModel().getColumn(i).setMaxWidth(0);
                table.getColumnModel().getColumn(i).setWidth(0);
            }

        }

        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));

        autoResizeJTableColWidth(table);

        JPanel panelHeader = new JPanel();
        panelHeader.setLayout(new BoxLayout(panelHeader, BoxLayout.Y_AXIS));
        panelHeader.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        JButton eliminaComunicazioneBut = new JButton("Elimina comunicazione selezionata");
        eliminaComunicazioneBut.setAlignmentX(Component.LEFT_ALIGNMENT);

        eliminaComunicazioneBut.addActionListener(e -> {
            int selectedRow = table.getSelectedRow(); // select a row
            if (selectedRow != -1) {
                int idSollecitoIndex = table.getColumn(getColName("idSollecito")).getModelIndex();
                String idSollecito = table.getValueAt(selectedRow, idSollecitoIndex).toString();
                int idRagSocIndex = table.getColumn(getColName("Cliente")).getModelIndex();
                String ragSoc = table.getValueAt(selectedRow, idRagSocIndex).toString();
                int scelta = JOptionPane.showConfirmDialog(null, "Sei sicuro di voler eliminare questa comunicazione? \nCliente: " + ragSoc, "Conferma", JOptionPane.YES_NO_OPTION);
                if (scelta == JOptionPane.YES_OPTION) {
                    int rowAffected = SollecitoModel.eliminaComunicazioneDalDb(idSollecito);
                    DefaultTableModel model = (DefaultTableModel) table.getModel();
                    model.removeRow(selectedRow);
                    table.revalidate();
                }
            }

        });

        // System.out.println(secondPane.getWidth() + "," + secondPane.getHeight());
        panelHeader.add(eliminaComunicazioneBut);
        panel.add(panelHeader);
        // Aggiunge lo JScrollPane al centro del JPanel
        panel.add(scrollPaneForTable, BorderLayout.CENTER);
        //panel.add(scrollPaneForTable);
        return panel;
    }


    public static void main(String[] args) {
    /*    String connectionUrl = "**************************************************;";
        connectionUrl = connectionUrl + "databaseName=LedbActi;user=Leuserdbd;password=********";
        //Db activoDb=new Db(connectionUrl);
        Db.initDatabase(connectionUrl);


        //StoricoSollecitiGui();*/

/*
        enum TipoDiEmail {
            SOLLECITO,
            CORTESIA,
        }

        TipoDiEmail tipoMailDaInviare = TipoDiEmail.SOLLECITO;


        JRadioButton radioSollecito = new JRadioButton("Sollecito fatture non pagate");
        radioSollecito.setSelected(true);
        JRadioButton radioCortesia = new JRadioButton("Fattura di cortesia in PDF");
        ButtonGroup buttonGroup = new ButtonGroup();
        buttonGroup.add(radioSollecito);
        buttonGroup.add(radioCortesia);

        JPanel tipoMailPanel = new JPanel();
        tipoMailPanel.setLayout(new BoxLayout(tipoMailPanel, BoxLayout.Y_AXIS));
        tipoMailPanel.add(radioSollecito);
        tipoMailPanel.add(radioCortesia);

        Object[] opzioniTipoMail = {tipoMailPanel, "Continua"};
        String popupMes = "<html><center>Seleziona il tipo di email da inviare al cliente<hr></center></html>";
        int scelta = JOptionPane.showOptionDialog(null, popupMes, "Tipo di comunicazione al cliente", JOptionPane.OK_CANCEL_OPTION, JOptionPane.QUESTION_MESSAGE, null, opzioniTipoMail, null);

        System.out.println("radioButton1 = " + radioSollecito.isSelected());
        System.out.println("radioButton2 = " + radioCortesia.isSelected());
        */


    }


}
