package actiweb.activo.controllers;

import actiweb.activo.models.ClienteModel;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;

public class Clienti {
/*    String ragioneSociale = "";
    String idCliente = "";
    String codice = "";
    String codiceF = "";
    String partitaIva = "";
    String email = "";


    String indirizzo = "";
    String citta = "";*/

    static ArrayList<ClienteModel> listaClientiCaricata = null;

    public static ClienteModel getClienteData(String clienteId) throws SQLException {

        ArrayList<ClienteModel> listaClienti = Clienti.getListaClienti();

        for (ClienteModel thisClient : listaClienti) {
            if (thisClient.getIdCliente().equals(clienteId)) {
                return thisClient;
            }
        }
        return null;
    }

    public static ArrayList<ClienteModel> getListaClienti() throws SQLException {

        if (listaClientiCaricata != null) {
            //System.out.println("ListaClienti caricata dalla cache");
            return listaClientiCaricata;
        }

        ArrayList<ClienteModel> listOfClients = new ArrayList<ClienteModel>();

        final String SQL = "select * from clienti ORDER BY RagioneSociale";


        // try (Statement statement = Db.getDbConn().createStatement();) {

        try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL); ResultSet rs = prepStm.executeQuery()) {

            while (rs.next()) {
                //System.out.println(rs.getString("RagioneSociale")+" id="+rs.getString("idCliente")); //stampa le colonne della tabella
                ClienteModel tmpCliente = new ClienteModel();
                tmpCliente.setRagioneSociale(rs.getString("RagioneSociale"));
                tmpCliente.setIdCliente(rs.getString("idCliente"));
                tmpCliente.setPartitaIva(rs.getString("PartitaIva"));
                tmpCliente.setEmail(rs.getString("email"));
                listOfClients.add(tmpCliente);
            }
            listaClientiCaricata = listOfClients;
            return listOfClients;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

     /*try (PreparedStatement prepStm = con.prepareStatement(SQL); ResultSet rs = prepStm.executeQuery();) {

         while (rs.next()) {
             //System.out.println(rs.getString("RagioneSociale")+" id="+rs.getString("idCliente")); //stampa le colonne della tabella
             Cliente tmpCliente = new Cliente();
             tmpCliente.setRagioneSociale(rs.getString("RagioneSociale"));
             tmpCliente.setIdCliente(rs.getString("idCliente"));
             tmpCliente.setPartitaIva(rs.getString("PartitaIva"));
             tmpCliente.setIdCliente(rs.getString("idCliente"));
             listOfClients.onclick(tmpCliente);
         }
         return listOfClients;

     } catch (SQLException e) {
         throw new RuntimeException(e);
     }
*/
    }

/*
    public String getRagioneSociale() {
        return ragioneSociale;
    }

    public void setRagioneSociale(String ragioneSociale) {
        this.ragioneSociale = ragioneSociale;
    }

    public String getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(String idCliente) {
        this.idCliente = idCliente;
    }

    public String getCodice() {
        return codice;
    }

    public void setCodice(String codice) {
        this.codice = codice;
    }

    public String getCodiceF() {
        return codiceF;
    }

    public void setCodiceF(String codiceF) {
        this.codiceF = codiceF;
    }

    public String getPartitaIva() {
        return partitaIva;
    }

    public void setPartitaIva(String partitaIva) {
        this.partitaIva = partitaIva;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIndirizzo() {
        return indirizzo;
    }

    public void setIndirizzo(String indirizzo) {
        this.indirizzo = indirizzo;
    }

    public String getCitta() {
        return citta;
    }

    public void setCitta(String citta) {
        this.citta = citta;
    }*/
}
