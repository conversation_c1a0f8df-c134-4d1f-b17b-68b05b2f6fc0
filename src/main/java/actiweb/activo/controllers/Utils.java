package actiweb.activo.controllers;

import javax.swing.*;
import javax.swing.table.*;
import java.awt.*;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;

public class Utils {
    // colori: https://www.colorhexa.com/color-names
    public static Color GCOLOR1 = Color.decode("#fafad2"); // banana
    public static Color GCOLOR1a = Color.decode("#faf0e6"); // banana3
    public static Color GCOLOR1b = Color.decode("#fae7b5"); // banana
    public static Color GCOLOR2 = Color.decode("#f08080"); // light coral
    public static Color GCOLOR3 = Color.decode("#00bfff"); // deep Sky blue
    public static Color GCOLOR4 = Color.decode("#d3d3d3"); // light gray

    public static JDialog alert(String mes) {

        JDialog dialog = new JDialog();

        dialog.setDefaultCloseOperation(JDialog.DISPOSE_ON_CLOSE);

        dialog.setLayout(new BorderLayout());
        // dialog.setLayout(new BorderLayout());

        // JLabel messageLabel = new JLabel(mes);

        String testoConBr = mes.replaceAll("\n", "<br>");
        JLabel messageLabel = new JLabel("<html>" + testoConBr + "</html>");

        JPanel panelHeader = new JPanel();
        panelHeader.setLayout(new BoxLayout(panelHeader, BoxLayout.LINE_AXIS));
        panelHeader.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        panelHeader.add(messageLabel);
        dialog.add(panelHeader);

        // dialog.add(messageLabel, BorderLayout.CENTER);

        dialog.setSize(350, 250);
        dialog.setLocationRelativeTo(null); // Posiziona la finestra al centro dello schermo
        // dialog.setModal(true); // Blocca l'interazione con altre finestre
        dialog.pack();
        dialog.repaint();
        dialog.revalidate();
        dialog.setVisible(true);
        return dialog;
    }

    public static String getColName(String key) {
        return getColName(key, false);
    }

    public static String getColName(String key, Boolean isHidden) {
        HashMap<String, String> translationMap = new HashMap<>();

        translationMap = new HashMap<>();
        translationMap.put("numFattura", "Numero fattura");
        translationMap.put("idFattura", "idFattura");
        translationMap.put("data", "Data");
        translationMap.put("totalePagato", "Totale Pagato");
        translationMap.put("idCliente", "idCliente");
        translationMap.put("totale", "Totale");
        translationMap.put("testoFattura", "Testo Fattura");
        translationMap.put("Tipologia", "Tipologia");
        translationMap.put("RagioneSociale", "Ragione Sociale");

        String hidden = "";
        if (isHidden) {
            hidden = "<hidden>";
        }

        String translation = translationMap.get(key);
        if (translation != null) {
            return translation + hidden;
        } else {
            return key + hidden;
        }

    }

    public static double parseDecimal(String input) {

        try {
            NumberFormat numberFormat = NumberFormat.getNumberInstance(Locale.getDefault());
            ParsePosition parsePosition = new ParsePosition(0);
            Number number = numberFormat.parse(input, parsePosition);
            if (parsePosition.getIndex() != input.length()) {
                throw new Exception("Invalid input" + parsePosition.getIndex());
                // System.out.println("Invalid input "+parsePosition.getIndex());
            }
            return number.doubleValue();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // return Double.parseDouble(input.replace(',','.'));
    }

    public static String formatMoney(String importo) {
        return formatMoney(Double.parseDouble(importo));
        // return formatMoney(Double.parseDouble(importo.replace(',','.')));
    }

    public static String formatData(String dataString) {
        // SimpleDateFormat format = new SimpleDateFormat("yyyy-dd-MM HH:mm:ss");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            // Date date1 = format.parse(dataString.replace("T", " "));
            // Date date1 = format.parse(dataString);
            Date date1 = parseDate(dataString);
            String d = new SimpleDateFormat("dd/MM/yyyy").format(date1);
            return d;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    public static Date convertStringToDate(String dateString) {
        String defaultPattern = "dd/MM/yyyy";
        return convertStringToDate(dateString, defaultPattern);
    }

    public static Date convertStringToDate(String dateString, String pattern) {
        if (pattern.isBlank()) {
            // pattern="yyyy-MM-dd HH:mm:ss";
            pattern = "dd/MM/yyyy";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDate localDate = LocalDate.parse(dateString, formatter);
        return java.sql.Date.valueOf(localDate);
    }

    public static String changeDateStrFormat(String dateStr, String inputPattern, String outputPattern) {

        DateTimeFormatter formatterIngresso = DateTimeFormatter.ofPattern(inputPattern);
        DateTimeFormatter formatterUscita = DateTimeFormatter.ofPattern(outputPattern);

        LocalDate dataLocale = LocalDate.parse(dateStr, formatterIngresso);
        String dataFormattata = dataLocale.format(formatterUscita);

        return dataFormattata;
        /*
         * if (inputPattern.isBlank()) {
         * inputPattern = "yyyy-MM-dd";
         * }
         * if (outputPattern.isBlank()) {
         * outputPattern = "dd/MM/yyyy";
         * }
         *
         * DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(inputPattern);
         * // Parsing della stringa di input in un oggetto LocalDate
         * LocalDate date = LocalDate.parse(dateStr, inputFormatter);
         * // Creazione del formatter per il formato di output
         * DateTimeFormatter outputFormatter =
         * DateTimeFormatter.ofPattern(outputPattern);
         * // Formattazione della data nel formato desiderato
         * return date.format(outputFormatter);
         */
    }

    public static String convertDateToString(Date myDate) {
        String inputPattern = "yyyy-MM-dd";
        String outputPattern = "dd/MM/yyyy";
        return convertDateToString(myDate, inputPattern, outputPattern);
    }

    /*
     * public static String convertDateToString(Date myDate) {
     * String inputPattern = "yyyy-MM-dd";
     * String outputPattern = "dd/MM/yyyy";
     * return convertDateToString(myDate, inputPattern, outputPattern);
     * }
     *
     * public static String convertDateToString(Date myDate, String inputPattern,
     * String outputPattern) {
     *
     * if (outputPattern.isBlank()) {
     * outputPattern = "dd/MM/yyyy";
     * }
     * System.out.println(myDate.toString());
     * Date newDate = (Date) myDate;
     * try {
     * LocalDate localDate =
     * newDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
     * System.out.println("localDate " + localDate);
     *
     * // Definisci il formato data italiano
     * //DateTimeFormatter formatter =
     * DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT);
     * //DateTimeFormatter italianFormatter =
     * formatter.withLocale(java.util.Locale.ITALIAN);
     *
     * DateTimeFormatter italianFormatter =
     * DateTimeFormatter.ofPattern(outputPattern);
     *
     * // Formatta la data nel formato italiano
     * String formattedDate = localDate.format(italianFormatter);
     * return formattedDate;
     * } catch (Exception e) {
     * throw new RuntimeException(e);
     * }
     * }
     */

    public static String convertDateToString(Date myDate, String inputPattern, String outputPattern) {
        if (inputPattern.isBlank()) {
            inputPattern = "yyyy-MM-dd";
        }
        if (outputPattern.isBlank()) {
            outputPattern = "dd/MM/yyyy";
        }
        String dateStr = myDate.toString();
        // Creazione del formatter per il formato di input
        DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(inputPattern);
        // Parsing della stringa di input in un oggetto LocalDate
        LocalDate date = LocalDate.parse(dateStr, inputFormatter);
        // Creazione del formatter per il formato di output
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(outputPattern);
        // Formattazione della data nel formato desiderato
        return date.format(outputFormatter);
    }

    public static Date parseDate(String dateString) {
        DateTimeFormatter[] formatters = new DateTimeFormatter[] {

                DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss zzz yyyy"),
                DateTimeFormatter.ofPattern("EEE MMM d H:mm:ss zzz yyyy"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
                DateTimeFormatter.ofPattern("yyyy-dd-MM HH:mm:ss"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                DateTimeFormatter.ofPattern("dd-MM-yyyy"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("d EEE yyyy"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("dd/MM/yy"),
        };

        for (DateTimeFormatter formatter : formatters) {

            try {
                LocalDateTime dateTime = LocalDateTime.parse(dateString, formatter);
                System.out.println("[parseDate] Trovato pattern " + formatter + " per la data " + dateString);
                return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
            } catch (DateTimeParseException ignored) {
            }
        }

        throw new IllegalArgumentException("Formato data non valido per la data " + dateString);
    }

    public static String formatMoney(Double importo) {
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.0");
        // DecimalFormat decimalFormat = new DecimalFormat("#,##");
        // Format the double value using DecimalFormat
        String importoPagatoStr = decimalFormat.format(importo);
        if (importoPagatoStr.endsWith(",0")) {
            importoPagatoStr = importoPagatoStr.substring(0, importoPagatoStr.length() - 2);
        }
        return importoPagatoStr;
    }

    public static ArrayList<HashMap<String, Object>> resultSetToArrayList(ResultSet resultSet) throws SQLException {
        ArrayList<HashMap<String, Object>> list = new ArrayList<>();
        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        while (resultSet.next()) {
            HashMap<String, Object> row = new HashMap<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = metaData.getColumnName(i);
                Object columnValue = resultSet.getObject(i);
                boolean useDefaultInsert = true;

                /*
                 * if (columnValue != null) {
                 * System.out.println(columnName + ">" + columnValue.getClass());
                 * }
                 */

                if (columnValue instanceof java.lang.Integer) {
                    row.put(columnName, resultSet.getInt(i));
                    useDefaultInsert = false;
                }
                if ((columnValue instanceof java.math.BigDecimal) || (columnValue instanceof java.lang.Double)) {
                    row.put(columnName, resultSet.getDouble(i));
                    useDefaultInsert = false;
                }
                if ((columnValue instanceof java.sql.Timestamp)) {
                    // System.out.println(columnName + ">" + columnValue.getClass());
                    // Definisci il formato data italiano
                    /*
                     * DateTimeFormatter formatter =
                     * DateTimeFormatter.ofLocalizedDate(FormatStyle.SHORT);
                     * DateTimeFormatter italianFormatter =
                     * formatter.withLocale(java.util.Locale.ITALIAN);
                     * System.out.println("ss " + resultSet.getDate(i));
                     * // row.put(columnName, resultSet.getDate(i).toLocalDate());
                     */
                    row.put(columnName, resultSet.getDate(i));
                    useDefaultInsert = false;
                    // System.out.println("ss " + resultSet.getDate(i));
                }

                if (useDefaultInsert) {
                    row.put(columnName, columnValue);
                }

            }
            list.add(row);
        }

        return list;
    }

    // Funzione per creare una JTable utilizzando un ArrayList di HashMap
    public static JTable createJTableFromArrayList(ArrayList<HashMap<String, Object>> list,
            JTableCustomColumn customCol) {
        DefaultTableModel model = new DefaultTableModel();
        JTable table = new JTable(model);

        // Otteniamo un insieme di chiavi dalla mappa
        Set<String> keys = customCol.colNames.keySet();
        // Creiamo un array di chiavi utilizzando l'insieme di chiavi
        String[] keysArray = keys.toArray(new String[0]);

        for (String key : customCol.colNames.keySet()) {
            // System.out.println(key);
            model.addColumn(customCol.colNames.get(key));
        }

        // Aggiungere le righe alla tabella

        for (HashMap<String, Object> row : list) {
            Object[] values = new Object[keysArray.length];
            for (int i = 0; i < keysArray.length; i++) {
                String colKey = keysArray[i];
                Object cellValue = row.get(colKey);
                /*
                 * if (cellValue != null) {
                 * System.out.println(colKey + ">" + cellValue.getClass());
                 * }
                 */

                boolean useDefaultInsert = true;

                if (cellValue instanceof java.lang.Integer) {
                    int numero = Integer.parseInt(cellValue.toString());
                    values[i] = numero;
                    useDefaultInsert = false;
                }

                if (cellValue instanceof java.lang.Double) {
                    double numero = Double.parseDouble(cellValue.toString());
                    values[i] = numero;
                    useDefaultInsert = false;
                }
                if ((cellValue instanceof java.sql.Timestamp)) {
                    values[i] = cellValue;
                    useDefaultInsert = false;
                }

                if (useDefaultInsert) {
                    values[i] = cellValue;
                }

                if (customCol.colFuncMap.containsKey(colKey)) {
                    // se c'è una funzione associata che modifica questo valore la eseguo
                    /*
                     * String app = row.get(colKey).toString();
                     * app = customCol.colFuncMap.get(colKey).apply(app);
                     * values[i] = app;
                     */
                    /*
                     * cellValue = customCol.colFuncMap.get(colKey).apply(cellValue);
                     * values[i] = cellValue;
                     */
                    values[i] = customCol.colFuncMap.get(colKey).apply(values[i]);
                }
                /*
                 * if (values[i] != null) {
                 * System.out.println(colKey + ">>" + values[i].getClass());
                 * }
                 */

            }
            model.addRow(values);
        }

        // TableModel model = table.getModel();
        // Ottenere il numero di colonne
        int colCount = model.getColumnCount();
        // Scorrere le colonne
        for (int i = 0; i < colCount; i++) {
            // Ottenere il nome della colonna
            String columnName = model.getColumnName(i);
            if (columnName.toLowerCase().contains("tag")) {
                table.getColumnModel().getColumn(i).setMinWidth(250);
            }
            // se il nome della colonna contiene la parola hidden la nascondo
            if (columnName.contains("<hidden>")) {

                String cName = columnName.replaceAll("<hidden>", "");
                table.getColumnModel().getColumn(i).setHeaderValue(cName);

                table.getColumnModel().getColumn(i).setMinWidth(0);
                table.getColumnModel().getColumn(i).setMaxWidth(0);
                table.getColumnModel().getColumn(i).setWidth(0);
            }
            // Esegui azioni con il nome della colonna
            // System.out.println(columnName);
        }

        return table;
    }

    public static JTable putDbQuerySelectResultIntoJTable(String SQL, JTableCustomColumn customCol) {

        // final String SQL = "SELECT * from ElencoScarico where TotalePagato<=0 ORDER
        // BY Cliente";
        // final String SQL = "SELECT * from ElencoScarico where TotalePagato<=0 and
        // Year(DataFattura)>="+anno+" ORDER BY Cliente";//tipo=2 è nota di credito
        // and tipologia!=2

        try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL); ResultSet rs = prepStm.executeQuery()) {

            JTable tab = putResultSetIntoJTable(rs, customCol);
            return tab;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    private static JTable putResultSetIntoJTable(ResultSet resultSet, JTableCustomColumn customCol)
            throws SQLException {

        if (customCol.resultSetProcessor != null) {
            resultSet = customCol.resultSetProcessor.run(resultSet);
        }

        ResultSetMetaData metaData = resultSet.getMetaData();
        int columnCount = metaData.getColumnCount();

        Vector<String> columnNames = new Vector<>();
        for (int i = 1; i <= columnCount; i++) {
            String colKey = metaData.getColumnName(i);
            if (customCol.colNames.containsKey(colKey)) {
                columnNames.add(customCol.colNames.get(colKey));
            }
        }

        Vector<Vector<Object>> data = new Vector<>();
        while (resultSet.next()) {
            Vector<Object> row = new Vector<>();
            for (int i = 1; i <= columnCount; i++) {
                String colKey = metaData.getColumnName(i);
                if (customCol.colNames.containsKey(colKey)) {

                    if (customCol.colFuncMap.containsKey(colKey)) {
                        // se c'è una funzione associata che modifica questo valore la eseguo
                        /*
                         * String app = resultSet.getObject(i).toString();
                         * app = customCol.colFuncMap.get(colKey).apply(app);
                         */
                        Object app = resultSet.getObject(i).toString();
                        app = customCol.colFuncMap.get(colKey).apply(app);
                        row.add(app);
                        // row.add(resultSet.getObject(i));
                    } else {
                        // se è un numero lo formatto con solo 2 cifre dopo la virgola
                        if (resultSet.getObject(i).getClass().getSuperclass().equals(Number.class)) {

                            if ((resultSet.getObject(i).toString().contains(".")) ||
                                    (resultSet.getObject(i).toString().contains(","))) {
                                Double dval = Double.parseDouble(resultSet.getObject(i).toString());
                                String sVal = Utils.formatMoney(dval);
                                row.add(sVal);
                            } else {
                                row.add(resultSet.getObject(i));
                            }

                        } else if (resultSet.getObject(i).getClass().getSuperclass().equals(Date.class)) {
                            String dataOk = new SimpleDateFormat("dd/MM/yyyy").format(resultSet.getObject(i));

                            // row.add(dataOk); //da rimettere quando risolvo per l'auto ordinamento
                            row.add(resultSet.getDate(i));
                        } else {

                            row.add(resultSet.getObject(i));
                        }

                    }
                }

            }
            data.add(row);
        }

        JTable table = new JTable(data, columnNames);

        table.setFillsViewportHeight(true);
        table.setAutoCreateRowSorter(true);
        // Ottenere il TableModel associato alla JTable
        TableModel model = table.getModel();
        // Ottenere il numero di colonne
        int colCount = model.getColumnCount();
        // Scorrere le colonne
        for (int i = 0; i < colCount; i++) {
            // Ottenere il nome della colonna
            String columnName = model.getColumnName(i);

            // se il nome della colonna contiene la parola hidden la nascondo
            if (columnName.contains("<hidden>")) {

                String cName = columnName.replaceAll("<hidden>", "");
                table.getColumnModel().getColumn(i).setHeaderValue(cName);

                table.getColumnModel().getColumn(i).setMinWidth(0);
                table.getColumnModel().getColumn(i).setMaxWidth(0);
                table.getColumnModel().getColumn(i).setWidth(0);
            }
            // Esegui azioni con il nome della colonna
            // System.out.println(columnName);
        }

        return table;
    }

    /***
     *
     * ESEMPI
     * String relativeBase = "src/main/java/actiweb/activo/";
     * String xmlFilePath = resolveToAbsolutePath(relativeBase +
     * "assets/input.xml");
     */
    public static String resolveToAbsolutePath(String relativePath) {
        // String relativePath = "src/main/java/actiweb/activo/assets/templatepdf.xsl";

        Path currentWorkingDirectory = Paths.get("").toAbsolutePath();
        // Creare un oggetto Path per il percorso relativo
        Path relativePathObj = Paths.get(relativePath);
        // Risolvere il percorso relativo rispetto alla directory di lavoro corrente
        Path absolutePath = currentWorkingDirectory.resolve(relativePathObj);
        // Convertire il percorso assoluto in una stringa
        return absolutePath.toString();
    }

    public static void main(String[] args) throws SQLException {
        /*
         * String connectionUrl = "**************************************************;";
         * connectionUrl = connectionUrl +
         * "databaseName=LedbActi;user=Leuserdbd;password=********";
         *
         * Db.initDatabase(connectionUrl);
         *
         * // final String SQL =
         * "SELECT  * from ElencoScarico where TotalePagato<=0 and Year(DataFattura)>=2023 ORDER BY Cliente"
         * ;
         * final String SQL =
         * "SELECT Cliente, RagioneSociale, SUM(totale) AS SommaPrezzo, COUNT(totale) AS NumFatNonPagate\n"
         * + "FROM ElencoScarico t1\n" + "INNER JOIN Clienti t2\n" +
         * "ON t1.Cliente = t2.IdCliente\n" + "WHERE TotalePagato <= 0\n" +
         * "GROUP BY Cliente,RagioneSociale\n" + "ORDER BY SommaPrezzo DESC;\n";
         *
         * HashMap<String, String> hm = new HashMap<String, String>();
         * hm.put("Cliente", "IdCliente hidden");
         * hm.put("SommaPrezzo", "Totale da pagare");
         * hm.put("NumFatNonPagate", "Numero fatture non pagate");
         * hm.put("RagioneSociale", "Ragione Sociale");
         *
         * //customCol=new
         *
         * JTable res = putDbQuerySelectResultIntoJTable(SQL, hm);
         */
        // JTable tab=putResultSetIntoJTable(res);
    }

    /*
     * public static void runInSeparateProcess(String waitMes, Function<Object,
     * Object> funcToRun, Object... params) {
     * JDialog loading = alert(waitMes);
     *
     * Thread t = new Thread(() -> {
     * //do stuff
     * funcToRun.apply(params);
     *
     * loading.setVisible(false); // Nasconde il dialog
     * loading.dispose(); // Distrugge il dialog
     *
     * });
     * t.start(); // Avvia il thread
     * }
     */

    /***
     * Tenta di fare un resize automatico delle colonne di una jtable in base al
     * contenuto
     */
    public static void autoResizeJTableColWidth(JTable table) {
        if (!SwingUtilities.isEventDispatchThread()) {
            SwingUtilities.invokeLater(() -> autoResizeJTableColWidth(table));
            return;
        }

        if (table == null || table.getColumnCount() == 0)
            return;

        // Forza l'inizializzazione dell'header
        table.getTableHeader().setResizingColumn(table.getColumnModel().getColumn(0));
        table.getTableHeader().doLayout();

        final TableColumnModel columnModel = table.getColumnModel();
        final int totalRows = Math.min(table.getRowCount(), 1000); // Limite per sicurezza

        for (int column = 0; column < table.getColumnCount(); column++) {
            int width = 50; // Default minimo

            // Usa solo il renderer di base
            TableCellRenderer renderer = table.getDefaultRenderer(table.getColumnClass(column));

            for (int row = 0; row < totalRows; row++) {
                try {
                    Component comp = table.prepareRenderer(renderer, row, column);
                    width = Math.max(width, comp.getPreferredSize().width + 5); // Margine
                } catch (Exception e) {
                    width = Math.max(width, 50);
                }
            }

            columnModel.getColumn(column).setPreferredWidth(Math.min(width, 300));
        }

        table.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);
    }

    public static void autoResizeJTableColWidth2(JTable table) {
        final TableColumnModel columnModel = table.getColumnModel();

        TableCellRenderer renderer;
        Component comp;
        double width;

        for (int column = 0; column < table.getColumnCount(); column++) {
            // Account for header size
            System.out.println("qui=" + table.getColumnCount());
            width = table.getTableHeader().getHeaderRect(column).getWidth();

            for (int row = 0; row < table.getRowCount(); row++) {
                System.out.println("qua row=" + row + " col=" + column + " tot row=" + table.getRowCount());
                renderer = table.getCellRenderer(row, column);
                System.out.println("qua getCellRenderer=" + row + "/" + table.getRowCount());
                comp = table.prepareRenderer(renderer, row, column);
                width = Math.max(comp.getPreferredSize().width + 1, width);
            }
            if (width > 200)
                width = 200;
            columnModel.getColumn(column).setPreferredWidth((int) width);
        }
        // table.setAutoResizeMode(JTable.AUTO_RESIZE_NEXT_COLUMN);
        System.out.println("qui2");
        table.setAutoResizeMode(JTable.AUTO_RESIZE_ALL_COLUMNS);

        /*
         *
         * TableColumnModel columnModel = table.getColumnModel();
         * int numColumns = columnModel.getColumnCount();
         *
         * for (int i = 0; i < numColumns; i++) {
         * TableColumn column = columnModel.getColumn(i);
         * Component comp = table.getTableHeader().getDefaultRenderer()
         * .getTableCellRendererComponent(table, column.getHeaderValue(), false, false,
         * 0, 0);
         * int minWidth = comp.getPreferredSize().width;
         *
         * int maxWidth = 0;
         * for (int row = 0; row < table.getRowCount(); row++) {
         * comp = table.getDefaultRenderer(table.getModel().getColumnClass(i))
         * .getTableCellRendererComponent(table, table.getValueAt(row, i), false, false,
         * row, i);
         * maxWidth = Math.max(maxWidth, comp.getPreferredSize().width + 1);
         * }
         *
         * int preferredWidth = Math.max(minWidth, maxWidth);
         * if (preferredWidth > 350)
         * preferredWidth = 350;
         * if (preferredWidth < minWidth)
         * preferredWidth = minWidth;
         * column.setPreferredWidth(preferredWidth);
         * }
         */

    }

    public static boolean sonoPassatiAlmenoGiorni(Date data, int giorni) {
        // Ottieni la data corrente
        Date dataCorrente = new Date();

        // Calcola la differenza tra le due date
        long differenza = dataCorrente.getTime() - data.getTime();

        // Converti la differenza in giorni
        long giorniDifferenza = differenza / (24 * 60 * 60 * 1000);

        // Verifica se la differenza è maggiore o uguale al numero di giorni specificato
        return giorniDifferenza >= giorni;
    }

    public static boolean checkIfFolderExistOrCreate(String nomeCartella) {
        String directoryCorrente = System.getProperty("user.dir");
        System.out.println("Dir corrente " + directoryCorrente);

        File cartella = new File(directoryCorrente, nomeCartella);

        if (cartella.exists()) {
            // System.out.println("La cartella " + nomeCartella + " esiste");
            return true;
        } else {
            System.out.println("La cartella " + nomeCartella + " non esiste");
            if (cartella.mkdir()) {
                System.out.println("La cartella " + nomeCartella + " è stata creata");
                return true;
            } else {
                System.out.println("Impossibile creare la cartella " + nomeCartella);
                return false;
            }
        }
    }

    public interface ResultSetProcessor {
        ResultSet run(ResultSet rs);
    }

    /*
     * //usato per avere nomi custom e funzioni per le colonne
     *
     * Utils.JTableCustomColumn customCol=new Utils.JTableCustomColumn();
     *
     * customCol.colNames.put("ID", "id hidden"); //se contiene la parola hidden la
     * colonna viene nascosta
     * customCol.colNames.put("NumDoc", "Num fattura"); colonna NumDoc del Db avrà
     * nome "Num fattura"
     *
     * //esempio: per tutte le colonne del db che si chiamano "Totale" richiama
     * questa funzione
     * //che aggiunge "euro" dopo la cifra
     * customCol.colFuncMap.put("Totale", (importo) -> {
     * return importo+" euro";
     * });
     */

    public static class JTableCustomColumn {
        public LinkedHashMap<String, String> colNames = new LinkedHashMap<>();
        public HashMap<String, Function<Object, Object>> colFuncMap = new HashMap<>();
        // public HashMap<String, Function<String, String>> colFuncMap = new
        // HashMap<>();
        public ResultSetProcessor resultSetProcessor;
        String tempVal = "";

    }

    public static class JTableSpecialCellRenderer extends DefaultTableCellRenderer {

        ArrayList<Integer> righeDaEvidenziare = new ArrayList<>();
        Color evidColor = Color.decode("#ffff00");

        public void setEvidColor(Color evidColor) {
            this.evidColor = evidColor;
        }

        public void setRigheDaEvidenziare(ArrayList<Integer> righeDaEvidenziare) {
            this.righeDaEvidenziare = righeDaEvidenziare;
        }

        @Override
        public Component getTableCellRendererComponent(JTable table, Object value, boolean isSelected, boolean hasFocus,
                int row, int column) {
            Component component = super.getTableCellRendererComponent(table, value, isSelected, hasFocus, row, column);

            if (!isSelected) {
                // Controlla se la riga corrente è quella da colorare diversamente
                if (righeDaEvidenziare.contains(row)) {
                    // Imposta il colore di sfondo della riga come rosso
                    component.setBackground(evidColor);
                } else {
                    // Imposta il colore di sfondo della riga come bianco
                    component.setBackground(Color.WHITE);
                }
            }

            return component;
        }
    }

}

/*
 *
 * SELECT Cliente, RagioneSociale, SUM(totale) AS SommaPrezzo, COUNT(totale) AS
 * NumFatNonPagate
 * FROM ElencoScarico t1
 * INNER JOIN Clienti t2
 * ON t1.Cliente = t2.IdCliente
 * WHERE TotalePagato <= 0
 * GROUP BY Cliente,RagioneSociale
 * ORDER BY SommaPrezzo DESC;
 */
