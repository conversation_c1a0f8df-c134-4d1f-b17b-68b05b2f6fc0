package actiweb.activo.controllers;

import java.sql.*;

public class Db {
    private static final String connectionUrl_OLD = "jdbc:sqlserver://**************:1433;encrypt=false;databaseName=LedbActi;user=Leuserdbd;password=********";
    private static final String connectionUrl2 = "jdbc:sqlserver://***************:1433;encrypt=false;databaseName=LedbActi;user=Leuserdbd;password=********";


    private static final String DB_IP = "***************"; // Nuova variabile per l'IP
    private static final String DB_PORT = "1433";
    private static final String DB_NAME = "LedbActi";
    private static final String DB_USER = "Leuserdbd";
    private static final String DB_PASSWORD = "********";

    private static final String connectionUrl = "jdbc:sqlserver://" + DB_IP + ":" + DB_PORT + ";encrypt=false;databaseName=" + DB_NAME + ";user=" + DB_USER + ";password=" + DB_PASSWORD;


    public static Connection dbConn;

    // Getter per l'IP del database
    public static String getDbIp() {
        return DB_IP;
    }


    public static Connection getDbConnection() throws SQLException {
        try {

            Connection connection;
            connection = DriverManager.getConnection(connectionUrl);
            return connection;
        } catch (SQLException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static Connection getDbConn() {
        return dbConn;
    }

    public static void initDatabase() {
        try {
            dbConn = DriverManager.getConnection(connectionUrl);
            System.out.println("connesso");
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }

    //non funziona, chiude il recordset
    public static ResultSet query() {

        try (Statement statement = Db.getDbConn().createStatement()) {
            //try (Connection con = Db.getDbConn(); Statement statement = con.createStatement();) {

            String COLONNE_NELLA_TABELLA = """
                    select *
                    from INFORMATION_SCHEMA.COLUMNS
                    where TABLE_NAME='PrimaNotaSocieta'""";

            ResultSet rs = statement.executeQuery(COLONNE_NELLA_TABELLA);

            while (rs.next()) {
                System.out.println(rs.getString("COLUMN_NAME")); //stampa le colonne della tabella
            }
            return rs;
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

}
