package actiweb.activo.controllers;

import actiweb.activo.models.FatturaModel;
import actiweb.activo.views.MainGuiLayout;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumn;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.*;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.Year;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static actiweb.activo.controllers.Utils.*;
import static actiweb.activo.views.MainGuiLayout.caricaFattureCliente;
import static actiweb.activo.views.MainGuiLayout.openContainerPopup;

public class Fatture {

    public static String dettaglioFattura(String idFattura) throws SQLException {
        final String SQL = "select * from Scarico where IdScarico=" + idFattura;
        StringJoiner fullDetail = new StringJoiner("\n ");
        String quantStr = "";
        // fullDetail.onclick("Dettagli fattura \n");
        try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL); ResultSet rs = prepStm.executeQuery()) {

            while (rs.next()) {
                quantStr = "";
                if (rs.getInt("quantita") > 1) {
                    quantStr = " (" + rs.getInt("quantita") + "x" + rs.getDouble("prezzoven") + "€) ";
                }
                if (rs.getDouble("Totale") == 0) {
                    fullDetail.add(rs.getString("Nome") + " " + quantStr);
                } else {
                    fullDetail.add(
                            " " + rs.getString("Nome") + " " + rs.getDouble("Totale") + "€ +IVA " + quantStr + "\n");
                }

                // System.out.println("Dettaglio "+rs.getString("Nome")+"
                // Prezzo="+rs.getDouble("Totale"));
            }

        }
        fullDetail.add("\n");
        return fullDetail.toString();

    }

    /**
     * Recupera i dettagli per una lista di fatture.
     *
     * @param idFattura una lista di ID delle fatture da interrogare.
     * @return una Map che ha come chiave l'ID della fattura e come valore il
     *         dettaglio completo.
     * @throws SQLException se si verifica un errore di accesso al database.
     */
    public static Map<String, String> dettagliFattureMultiple(java.util.List<String> idFattura) throws SQLException {
        if (idFattura == null || idFattura.isEmpty()) {
            return Collections.emptyMap();
        }

        // Crea una stringa di placeholder (?,?,?) per la clausola IN
        String placeholders = String.join(",", Collections.nCopies(idFattura.size(), "?"));
        final String SQL = "SELECT * FROM Scarico WHERE IdScarico IN (" + placeholders + ") ORDER BY IdScarico";

        // Usiamo una mappa con StringJoiner per raggruppare le righe di dettaglio per
        // ogni fattura
        Map<String, StringJoiner> dettagliPerFattura = new LinkedHashMap<>();

        try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL)) {
            // Imposta i valori per i placeholder
            for (int i = 0; i < idFattura.size(); i++) {
                prepStm.setString(i + 1, idFattura.get(i));
            }

            try (ResultSet rs = prepStm.executeQuery()) {
                while (rs.next()) {
                    String idScarico = rs.getString("IdScarico");
                    String quantStr = "";
                    if (rs.getInt("quantita") > 1) {
                        quantStr = " (" + rs.getInt("quantita") + "x" + rs.getDouble("prezzoven") + "€) ";
                    }

                    String rigaDettaglio = (rs.getDouble("Totale") == 0)
                            ? rs.getString("Nome") + " " + quantStr
                            : " " + rs.getString("Nome") + " " + rs.getDouble("Totale") + "€ +IVA " + quantStr + "\n";

                    dettagliPerFattura.computeIfAbsent(idScarico, k -> new StringJoiner("\n ")).add(rigaDettaglio);
                }
            }
        }

        Map<String, String> risultatoFinale = new HashMap<>();
        dettagliPerFattura.forEach((id, joiner) -> risultatoFinale.put(id, joiner.toString()));
        return risultatoFinale;
    }

    public static ArrayList<FatturaModel> listaFatturePerCliente(String idCliente) throws SQLException {
        // final String idClienteStr=idCliente;

        ArrayList<FatturaModel> listaFatture = new ArrayList<FatturaModel>();
        final String SQL = "select * from ElencoScarico where Cliente=" + idCliente; // ORIGINALE
        // final String SQL = "select * from ElencoScarico,Scarico where
        // ElencoScarico.ID=";

        try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL); ResultSet rs = prepStm.executeQuery()) {
            // prepStm.setString(1, idClienteStr);
            // ResultSet rs = prepStm.executeQuery();) {

            while (rs.next()) {

                FatturaModel ft = new FatturaModel();
                ft.setIdFattura(rs.getString("ID"));
                ft.setNumFattura(rs.getString("NumDoc"));
                ft.setTestoFattura(rs.getString("Note"));
                ft.setDataFattura(rs.getDate("DataFattura"));
                ft.setPrezzoPagato(rs.getDouble("TotalePagato"));
                ft.setPrezzoTotaleFattura(rs.getDouble("totale"));

                if (rs.getDouble("tipologia") == 2) { // è una nota di credito
                    ft.setPrezzoPagato(rs.getDouble("TotalePagato") * -1);
                    ft.setNumFattura(rs.getString("NumDoc") + " (nota di credito)");
                    ft.setPrezzoTotaleFattura(rs.getDouble("totale") * -1);
                }

                listaFatture.add(ft);
                // System.out.println("FT="+ft.getNumFattura()+" > "+ft.getTestoFattura());
                // System.out.println("Id="+ft.getIdFattura()+" FT="+ft.getNumFattura()+" >
                // "+ft.getDataFattura()+" > "+rs.getDouble("TotalePagato"));
                // System.out.println("FT="+ft.getNumFattura()+" >
                // "+rs.getString("DataFattura"));
                // System.out.println("qua"+rs.getString("ID"));
            }
            return listaFatture;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }

    public static ArrayList<FatturaModel> listaFattureNonPagate(String SQL, Year annoInizio) throws SQLException {

        if (annoInizio == null) {
            annoInizio = Year.of(2015);
        }
        final String anno = annoInizio.toString();
        // final String idClienteStr=idCliente;
        ArrayList<FatturaModel> listaFatture = new ArrayList<FatturaModel>();
        // final String SQL = "SELECT * from ElencoScarico where TotalePagato<=0 ORDER
        // BY Cliente";

        // and tipologia!=2

        try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL); ResultSet rs = prepStm.executeQuery()) {

            while (rs.next()) {
                FatturaModel ft = new FatturaModel();
                ft.setIdFattura(rs.getString("ID"));
                ft.setIdCliente(rs.getString("Cliente"));
                ft.setNumFattura(rs.getString("NumDoc"));
                ft.setTestoFattura(rs.getString("Note"));
                ft.setDataFattura(rs.getDate("DataFattura"));
                ft.setPrezzoPagato(rs.getDouble("TotalePagato"));
                ft.setPrezzoImponibile(rs.getDouble("imponibile"));
                ft.setPrezzoTotaleFattura(rs.getDouble("totale"));

                if (rs.getDouble("tipologia") == 2) { // è una nota di credito
                    ft.setPrezzoPagato(rs.getDouble("TotalePagato") * -1);
                    ft.setNumFattura(rs.getString("NumDoc") + " (nota di credito)");
                    ft.setPrezzoTotaleFattura(rs.getDouble("totale") * -1);
                }

                listaFatture.add(ft);
                // System.out.println("FT="+ft.getNumFattura()+" > "+ft.getTestoFattura());
                // System.out.println("FT=" + ft.getNumFattura() + " > " + ft.getDataFattura() +
                // " > " + rs.getDouble("TotalePagato"));
                // System.out.println("FT="+ft.getNumFattura()+" >
                // "+rs.getString("DataFattura"));
                // System.out.println(rs.getString("Data"));
            }

            return listaFatture;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    public static String creaPdfDaXml(String idFattura) {
        // String xsltPath =
        // "src/main/java/actiweb/activo/assets/template-fattura-asso-actiweb.xsl";
        String xsltPath = "src/main/resources/assets/template-fattura-asso-actiweb.xsl";
        String pdfPath = "";
        String pdfDestFolder = "fatture";
        try {
            FatturaModel ft = caricaXMLFatturaElettronica(idFattura);
            if ((ft.getXMLFattura() != null) && (!ft.getXMLFattura().isEmpty())) {
                String nomefilePdf = ft.buildFileName("pdf");
                String xmlPath = ft.buildFullFilenamePath("", "xml");

                if (checkIfFolderExistOrCreate(pdfDestFolder)) {
                    pdfPath = ft.buildFullFilenamePath(pdfDestFolder, "pdf");
                    // pdfPath = ft.buildFullFilenamePath(pdfDestFolder, "pdf", true);
                    // pdfPath = ft.buildFullFilenamePath(pdfDestFolder + "/", "pdf");
                } else {
                    throw new Exception("Errore nel trovare o creare la cartella " + pdfDestFolder);
                }

                String htmlPath = ft.buildFullFilenamePath("", "htm");
                // salvo l'xml della fattura caricato dal db dentro un file in xmlPath
                scriviStringaSuFile(xmlPath, ft.getXMLFattura());
                // converto e salvo in html il file xml usando il modello xsl
                convertXmlToHtml(xmlPath, xsltPath, htmlPath);
                // converto il file html in pdf
                convertHtmlToPdf(htmlPath, pdfPath);

                // JOptionPane.showMessageDialog(null, "File " + nomefilePdf + " creato per la
                // fattura!");

                deleteTmpFile(xmlPath);
                deleteTmpFile(htmlPath);
                // deleteTmpFile(xmlPath);
            } else {
                String messaggio = "Errore \nXML della fattura è vuoto oppure la fattura non è stata trovata!";
                JOptionPane.showMessageDialog(null, messaggio);
                throw new Exception("XML della fattura è vuoto!");
            }
            System.out.println("Conversione PDF completata!");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pdfPath;
    }

    private static void convertXmlToHtml(String xmlPath, String xsltPath, String htmlPath) throws Exception {

        // controllo se esiste il file xml e provo a rinominarlo col suo stesso nome per
        // vedere se ha finito di scrivere
        File xmlFile = new File(xmlPath);
        if ((xmlFile.exists()) && (xmlFile.renameTo(xmlFile))) {

            try {
                /*
                 * 
                 * //System.out.println(xmlPath + " esiste ed è completamente scritto");
                 * TransformerFactory transformerFactory = new
                 * net.sf.saxon.TransformerFactoryImpl();
                 * Transformer transformer = transformerFactory.newTransformer(new
                 * StreamSource(new File(xsltPath)));
                 * transformer.transform(new StreamSource(new File(xmlPath)), new
                 * StreamResult(new FileOutputStream(new File(htmlPath))));
                 */
                TransformerFactory transformerFactory = new net.sf.saxon.TransformerFactoryImpl();

                InputStream xslStream = Fatture.class.getResourceAsStream("/assets/template-fattura-asso-actiweb.xsl");
                // Creiamo gli oggetti Source e Result per l'utilizzo del transformer
                Source xslSource = new StreamSource(xslStream);

                StreamResult result = new StreamResult(new FileOutputStream(new File(htmlPath)));
                Transformer transformer = transformerFactory.newTransformer(xslSource);
                // Applichiamo la trasformazione
                transformer.transform(new StreamSource(new File(xmlPath)), result);

            } catch (TransformerException e) {
                throw new RuntimeException(e);
            }

        }

    }

    private static void convertHtmlToPdf(String htmlPath, String pdfPath) throws Exception {

        File htmlFile = new File(htmlPath);
        if ((htmlFile.exists()) && (htmlFile.renameTo(htmlFile))) {
            try {
                // System.out.println(htmlPath + " esiste ed è completamente scritto");
                Document document = new Document();
                PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(new File(pdfPath)));
                document.open();
                /*
                 * String htmlContent =
                 * "<img src='file:///src/main/java/actiweb/activo/assets/testatafatt.png' />";
                 * XMLWorkerHelper.getInstance().parseXHtml(writer, document, new
                 * StringReader(htmlContent));
                 * String htmlContent2 =
                 * "<html><body><h1>Hello, Worldffdfdd!</h1></body></html>";
                 * XMLWorkerHelper.getInstance().parseXHtml(writer, document, new
                 * StringReader(htmlContent2));
                 */
                XMLWorkerHelper.getInstance().parseXHtml(writer, document, new FileInputStream(new File(htmlPath)));
                document.close();
                writer.close();
                System.out.println(htmlPath + " -> " + pdfPath + " scritto");
            } catch (DocumentException | IOException e) {
                throw new RuntimeException(e);
            }
        }

    }

    private static FatturaModel caricaXMLFatturaElettronica(String idFattura) {

        FatturaModel ft = new FatturaModel();
        final String SQL = "SELECT *,YEAR(DataFattura) AS AnnoFattura from ElencoScarico WHERE tipologia!=2 AND ID ="
                + idFattura; // 1999

        try (PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL); ResultSet rs = prepStm.executeQuery()) {
            while (rs.next()) {

                ft.setIdFattura(rs.getString("ID"));
                ft.setNumFattura(rs.getString("NumDoc"));
                ft.setTestoFattura(rs.getString("Note"));
                ft.setDataFattura(rs.getDate("DataFattura"));
                ft.setPrezzoPagato(rs.getDouble("TotalePagato"));
                ft.setPrezzoTotaleFattura(rs.getDouble("totale"));
                ft.setXMLFattura(rs.getString("FatturaElettronica"));
                ft.setAnnoFattura(rs.getInt("AnnoFattura"));
            }
            // System.out.println(ft.getXMLFattura().length());
            return ft;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    private static void scriviStringaSuFile(String percorsoFile, String contenuto) {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(percorsoFile))) {
            writer.write(contenuto);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void deleteTmpFile(String path) {
        File tmpFile = new File(path);
        if (tmpFile.exists()) {
            boolean deleted = tmpFile.delete();
            if (deleted) {
                // System.out.println(path + " cancellato correttamente.");
            } else {
                System.out.println("Impossibile cancellare " + path);
            }
        }
    }

    public static JPanel buildFatturatoMensileTableGui(ArrayList<HashMap<String, Object>> fatturatoMensileList) {

        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));

        JPanel panelTableContainer = new JPanel();
        panelTableContainer.setLayout(new BoxLayout(panelTableContainer, BoxLayout.Y_AXIS));

        if (fatturatoMensileList.isEmpty()) {
            JPanel panel1 = new JPanel();
            JLabel noRes = new JLabel("Nessun risultato");
            panel1.add(noRes);
            return panel1;
            // return new JPanel().add(new JLabel("Nessun risultato"));
        }

        Utils.JTableCustomColumn customCol = new Utils.JTableCustomColumn();
        customCol.colNames.put("periodo", "Periodo<hidden>");
        customCol.colNames.put("Cliente", "IdCliente<hidden>");
        customCol.colNames.put("RagioneSociale", "Ragione Sociale");
        customCol.colNames.put("NumDoc", "Numero Fattura");
        customCol.colNames.put("Totale", "Totale Fattura");
        customCol.colNames.put("TotalePagato", "Totale Pagato");

        LinkedHashMap<String, ArrayList<HashMap<String, Object>>> fatturatoMensileMap = new LinkedHashMap<String, ArrayList<HashMap<String, Object>>>();
        for (HashMap<String, Object> questaFattura : fatturatoMensileList) {
            String periodo = questaFattura.get("periodo").toString();
            if (fatturatoMensileMap.containsKey(periodo)) {
                fatturatoMensileMap.get(periodo).add(questaFattura);
            } else {
                // Creazione di una nuova lista con l'elemento e aggiunta al map con la chiave
                ArrayList<HashMap<String, Object>> lista = new ArrayList<>();
                lista.add(questaFattura);
                fatturatoMensileMap.put(periodo, lista);
            }
        }
        Set<String> fatMensileKeys = fatturatoMensileMap.keySet();
        double fatturatoAnno = 0.0;
        int numeroFattureAnno = 0;
        double incassatoAnno = 0;
        String anno = String.valueOf(Year.now().getValue());
        for (String rk : fatMensileKeys) {
            // System.out.println("Cliente id=" + rk);
            ArrayList<HashMap<String, Object>> ftList = fatturatoMensileMap.get(rk);
            JTable fatturatoMensileTableTmp;

            double totaleFatturatoMese = 0.0;
            double totaleIncassatoMese = 0.0;

            for (int i = 0; i < ftList.size(); i++) {
                HashMap<String, Object> questaFatt = ftList.get(i);

                if (ftList.get(i).get("Tipologia").toString().equals("2")) {
                    Double prezzoNegativo = Double.parseDouble(ftList.get(i).get("Totale").toString()) * -1;
                    ftList.get(i).replace("Totale", prezzoNegativo);
                    prezzoNegativo = Double.parseDouble(ftList.get(i).get("TotalePagato").toString()) * -1;
                    ftList.get(i).replace("TotalePagato", prezzoNegativo);
                    ftList.get(i).replace("NumDoc", ftList.get(i).get("Tipologia").toString() + " (nota di credito)");
                }
                totaleFatturatoMese = totaleFatturatoMese + (Double) questaFatt.get("Totale");
                totaleIncassatoMese = totaleIncassatoMese + (Double) questaFatt.get("TotalePagato");
                ftList.get(i).replace("Totale", Utils.formatMoney(ftList.get(i).get("Totale").toString()));
                ftList.get(i).replace("TotalePagato", Utils.formatMoney(ftList.get(i).get("TotalePagato").toString()));
            }

            fatturatoAnno = fatturatoAnno + totaleFatturatoMese;
            incassatoAnno = incassatoAnno + totaleIncassatoMese;
            numeroFattureAnno = numeroFattureAnno + ftList.size();

            fatturatoMensileTableTmp = createJTableFromArrayList(ftList, customCol);

            /*
             * JTable fatturatoMensileTableTmp;
             * fatturatoMensileTableTmp = fatturatoMensileJTable(fatturatoMensileList);
             */

            ArrayList<Integer> righeContenentiFattureNonPagate = new ArrayList<>();
            DefaultTableModel model = (DefaultTableModel) fatturatoMensileTableTmp.getModel();
            int colIndex = 0;
            int colIndex2 = 0;
            for (int row = 0; row < model.getRowCount(); row++) {
                colIndex = fatturatoMensileTableTmp.getColumn(customCol.colNames.get("Totale")).getModelIndex();
                colIndex2 = fatturatoMensileTableTmp.getColumn(customCol.colNames.get("TotalePagato")).getModelIndex();

                /*
                 * colIndex =
                 * fatturatoMensileTableTmp.getColumn(getColName("Totale")).getModelIndex();
                 * colIndex2 =
                 * fatturatoMensileTableTmp.getColumn(getColName("TotalePagato")).getModelIndex(
                 * );
                 */

                NumberFormat nf = NumberFormat.getInstance();
                try {
                    double questaFatturaTotale = nf.parse(model.getValueAt(row, colIndex).toString()).doubleValue();
                    double questaFatturaPagato = nf.parse(model.getValueAt(row, colIndex2).toString()).doubleValue();
                    if (questaFatturaPagato != questaFatturaTotale) {
                        // aggiungo questa riga a quelle da evidenziare perchè non pagate
                        righeContenentiFattureNonPagate.add(row);
                    }
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            Utils.JTableSpecialCellRenderer cellRenderer = new Utils.JTableSpecialCellRenderer();
            cellRenderer.setEvidColor(GCOLOR1);
            cellRenderer.setRigheDaEvidenziare(righeContenentiFattureNonPagate);
            fatturatoMensileTableTmp.setDefaultRenderer(Object.class, cellRenderer);

            LocalDate data = LocalDate.parse(rk + "-01");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM yyyy");
            String outputPeriodo = data.format(formatter);

            /*
             * if (anno.isBlank()) {
             * formatter = DateTimeFormatter.ofPattern("yyyy");
             * anno = data.format(formatter);
             * }
             */
            formatter = DateTimeFormatter.ofPattern("yyyy");
            anno = data.format(formatter);

            StringBuilder titPeriodo = new StringBuilder();
            titPeriodo.append("<html><h3><u> ").append(outputPeriodo.toUpperCase());
            titPeriodo.append("</u> - Emesse: ").append(ftList.size()).append(" fatture");
            titPeriodo.append(" - Importo: ").append(Utils.formatMoney(totaleFatturatoMese)).append("€");
            titPeriodo.append(" - Da incassare: ").append(Utils.formatMoney(totaleFatturatoMese - totaleIncassatoMese))
                    .append("€");
            titPeriodo.append(" - Fatturato annuo finora: ").append(Utils.formatMoney(fatturatoAnno)).append("€");
            titPeriodo.append("</h3>").append("</html>");

            JLabel titoloPeriodo = new JLabel(String.valueOf(titPeriodo));
            titoloPeriodo.setHorizontalAlignment(SwingConstants.CENTER);
            titoloPeriodo.setAlignmentX(Component.CENTER_ALIGNMENT);
            // autoResizeJTableColWidth(fatturatoMensileTableTmp);

            JScrollPane scrollPaneForTableMonth = new JScrollPane(fatturatoMensileTableTmp);
            Dimension headerDim = fatturatoMensileTableTmp.getTableHeader().getPreferredSize();
            Dimension tableDim = fatturatoMensileTableTmp.getPreferredSize();
            Double hh = headerDim.getHeight() + tableDim.getHeight() + 3;
            Dimension totalTableDim = new Dimension(fatturatoMensileTableTmp.getWidth(), hh.intValue());
            scrollPaneForTableMonth.setPreferredSize(totalTableDim);
            scrollPaneForTableMonth.setVerticalScrollBarPolicy(ScrollPaneConstants.VERTICAL_SCROLLBAR_NEVER);

            JLabel spacerBR = new JLabel("<html><br></html>");

            // panelTableContainer.add(spacerBR);
            panelTableContainer.add(titoloPeriodo);
            panelTableContainer.add(scrollPaneForTableMonth);
            panelTableContainer.add(spacerBR);

            fatturatoMensileDoubleClickHandler(fatturatoMensileTableTmp);

        }

        JScrollPane scrollPaneForTable = new JScrollPane(panelTableContainer);

        JScrollBar verticalScrollBar = scrollPaneForTable.getVerticalScrollBar();
        verticalScrollBar.setValue(verticalScrollBar.getMaximum()); // scrolla fino alla fine,bisogna farlo 2 volte: boh
        verticalScrollBar.setValue(verticalScrollBar.getMaximum()); // scrolla fino alla fine,bisogna farlo 2 volte: boh

        // autoResizeJTableColWidth(fatturatoMensileTable);

        StringBuilder titPanel = new StringBuilder();
        titPanel.append("<html><center><h2>Anno ").append(anno).append("<hr>");
        titPanel.append("Emesse: ").append(numeroFattureAnno).append(" fatture");
        titPanel.append(" - Importo: ").append(Utils.formatMoney(fatturatoAnno)).append("€");
        titPanel.append(" - Da incassare: ").append(Utils.formatMoney(fatturatoAnno - incassatoAnno)).append("€");
        titPanel.append("</h2></center>").append("</html>");

        JLabel titoloPanel = new JLabel(String.valueOf(titPanel));
        titoloPanel.setHorizontalAlignment(SwingConstants.CENTER);
        titoloPanel.setAlignmentX(Component.CENTER_ALIGNMENT);

        JPanel panelHeader = new JPanel();
        panelHeader.setLayout(new BoxLayout(panelHeader, BoxLayout.X_AXIS));

        int annoPrima = Integer.parseInt(anno) - 1;
        int annoDopo = Integer.parseInt(anno) + 1;

        JButton butAnnoPrecedente = new JButton("<< " + annoPrima);
        JButton butAnnoSuccessivo = new JButton(annoDopo + " >>");

        butAnnoPrecedente.addActionListener(e -> {
            Year annoScelto = Year.of(annoPrima);
            MainGuiLayout.fatturatoPerMeseGui(annoScelto);
        });
        butAnnoSuccessivo.addActionListener(e -> {
            Year annoScelto = Year.of(annoDopo);
            MainGuiLayout.fatturatoPerMeseGui(annoScelto);
        });

        panelHeader.add(Box.createHorizontalStrut(15));
        panelHeader.add(butAnnoPrecedente);
        panelHeader.add(Box.createHorizontalStrut(5));
        panelHeader.add(titoloPanel);
        panelHeader.add(Box.createHorizontalStrut(5));
        panelHeader.add(butAnnoSuccessivo);
        panelHeader.add(Box.createHorizontalStrut(15));

        panel.add(panelHeader);
        panel.add(scrollPaneForTable, BorderLayout.CENTER);

        return panel;
    }

    public static void fatturatoMensileDoubleClickHandler(JTable thisJtable) {
        thisJtable.setFocusable(false);
        thisJtable.addMouseListener(new MouseAdapter() {
            public void mouseClicked(MouseEvent me) {

                // to detect double click events
                if ((me.getClickCount() == 2) && (me.getButton() == MouseEvent.BUTTON1)) {
                    JTable target = (JTable) me.getSource();
                    int row = target.getSelectedRow(); // select a row
                    int idClienteIndex = target.getColumn(getColName("IdCliente")).getModelIndex();
                    int ragioneSocIndex = target.getColumn(getColName("RagioneSociale")).getModelIndex();
                    String idCliente = target.getValueAt(row, idClienteIndex).toString();
                    String RagioneSociale = target.getValueAt(row, ragioneSocIndex).toString();
                    System.out.println("carico fatture del cliente " + idCliente);

                    JPanel newWinPanel;
                    newWinPanel = openContainerPopup(RagioneSociale, 0.6, 0.6);

                    caricaFattureCliente(idCliente, null, newWinPanel);

                }

                // Crea un DefaultCellEditor personalizzato che non permette la modifica delle
                // celle
                DefaultCellEditor cellEditor = new DefaultCellEditor(new JTextField()) {
                    @Override
                    public boolean isCellEditable(EventObject event) {
                        return false; // Rende le celle non editabili
                    }
                };

                // Imposta l'editor personalizzato sulla JTable per tutte le colonne
                for (int column = 0; column < thisJtable.getColumnCount(); column++) {
                    TableColumn tableColumn = thisJtable.getColumnModel().getColumn(column);
                    tableColumn.setCellEditor(cellEditor);
                }
            }
        });

    }

}
