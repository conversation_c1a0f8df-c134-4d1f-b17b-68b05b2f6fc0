package actiweb.activo.controllers;


import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeBodyPart;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.swing.*;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.Properties;

public class EmailSender {

/*    static final String fromEmail = "<EMAIL>";
    static final String fromName = "ActiWeb Internet Solutions";
    static final String password = "qsK39h%XAe7"; // correct password for gmail id
    static final String smtpHost = "actiwebspace.com";*/

    static final String fromEmail = "<EMAIL>";
    static final String fromName = "ActiWeb Internet Solutions";
    static final String password = "996nB?u4a"; // correct password for gmail id
    static final String smtpHost = "actiwebspace.com";

    public static emailResult sendEmail(mailMsg mes) {
        return sendEmail(mes.getToEmail(), mes.getToBccEmail(), mes.getSubject(), mes.getBody(), mes.getAttachmentFileList());
    }

    public static emailResult sendEmail(String toEmail, String toBccEmail, String subject, String body, ArrayList<String> attachmentFileList) {
        StringBuilder retBuff = new StringBuilder();
        emailResult ret = new emailResult();

        try {
            Session session = connectToMailServer();
            if (session != null) {
                retBuff.append("Email inviata correttamente a ");

                MimeMessage msg = new MimeMessage(session);
                //set message headers
                msg.addHeader("Content-type", "text/HTML; charset=UTF-8");
                msg.addHeader("format", "flowed");
                msg.addHeader("Content-Transfer-Encoding", "8bit");
                msg.setFrom(new InternetAddress(fromEmail, fromName));
                msg.setReplyTo(InternetAddress.parse(fromEmail, false));
                msg.setSubject(subject, "UTF-8");
                msg.setSentDate(new Date());

                //msg.setText(body, "UTF-8");

                // Crea un oggetto MIME per il corpo del messaggio
                MimeBodyPart bodyPart = new MimeBodyPart();
                bodyPart.setText(body, "UTF-8");
                // Aggiungi gli oggetti MIME al messaggio
                Multipart multipart = new MimeMultipart();
                multipart.addBodyPart(bodyPart);

                if (!attachmentFileList.isEmpty()) {
                    for (String allegatoPath : attachmentFileList) {
                        System.out.println("Aggiungo allegato:" + allegatoPath);
                        // Crea un oggetto MIME per l'allegato
                        MimeBodyPart attachmentPart = new MimeBodyPart();
                        File file = new File(allegatoPath);
                        attachmentPart.attachFile(file);
                        attachmentPart.setFileName(file.getName());
                        multipart.addBodyPart(attachmentPart);
                    }

                }

                msg.setContent(multipart);

                //toEmail="<EMAIL>, <EMAIL>";
                msg.setRecipients(Message.RecipientType.TO, InternetAddress.parse(toEmail, true));
                retBuff.append(toEmail);
                if ((toBccEmail != null) && (toBccEmail.contains("@"))) {
                    // Aggiunta dei destinatari in copia nascosta
                    msg.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(toBccEmail, true));
                    retBuff.append(" \n(BCC:").append(toBccEmail).append(") ");
                }
                //System.out.println("Message is ready");
                Transport.send(msg);
                System.out.println("Message sent!");
                //System.out.println(retBuff);
                ret.retResult = true;
                ret.retStr = retBuff.toString();

            }
        } catch (Exception e) {
            JOptionPane.showMessageDialog(null, e.getMessage(), "Errore ", JOptionPane.INFORMATION_MESSAGE);
            e.printStackTrace();
        }

        return ret;
    }

    private static Session connectToMailServer() {

        System.out.println("SSLEmail Start");
        Properties props = new Properties();
        props.put("mail.smtp.host", smtpHost); //SMTP Host
        props.put("mail.smtp.socketFactory.port", "465"); //SSL Port
        props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory"); //SSL Factory Class
        props.put("mail.smtp.auth", "true"); //Enabling SMTP Authentication
        props.put("mail.smtp.port", "465"); //SMTP Port

        Authenticator auth = new Authenticator() {
            //override the getPasswordAuthentication method
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(fromEmail, password);
            }
        };

        System.out.println("Session created");
        Session session = Session.getDefaultInstance(props, auth);
        return session;

    }

/*    public static void main(String[] args) {

        mailMsg mes = new mailMsg("<EMAIL>", null, "sub2", "body test");
        mes.addAttachmentFile("fatture/actiweb_ft-24-2023.pdf");
        mes.addAttachmentFile("fatture/actiweb_ft-53-2023.pdf");
        //sendEmail("<EMAIL>","<EMAIL>","SSLEmail Testing Subject", "Testing Body 2");
        sendEmail(mes);
    }*/

    /**
     * Outgoing Mail (SMTP) Server
     * requires TLS or SSL: smtp.gmail.com (use authentication)
     * Use Authentication: Yes
     * Port for SSL: 465
     */
    public static class emailResult {
        String retStr = "Errore invio mail";
        Boolean retResult = false;
    }

    public static class mailMsg {
        String toEmail; //"<EMAIL>" oppure "<EMAIL>, <EMAIL>";
        String toBccEmail = ""; //"<EMAIL>" oppure "<EMAIL>, <EMAIL>";
        String subject;
        String body;

        ArrayList<String> attachmentFileList = new ArrayList<>();

        public mailMsg(String toEmail, String toBccEmail, String subject, String body) {
            this.toEmail = toEmail;
            this.toBccEmail = toBccEmail;
            this.subject = subject;
            this.body = body;
        }

        public boolean isBccMailset() {
            return (toBccEmail != null) && (!toBccEmail.isEmpty());
        }

        public String getToEmail() {
            return toEmail;
        }

        public void setToEmail(String toEmail) {
            this.toEmail = toEmail;
        }

        public String getToBccEmail() {
            return toBccEmail;
        }

        public void setToBccEmail(String toBccEmail) {
            this.toBccEmail = toBccEmail;
        }

        public String getSubject() {
            return subject;
        }

        public void setSubject(String subject) {
            this.subject = subject;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }


        public ArrayList<String> getAttachmentFileList() {
            return attachmentFileList;
        }

        public void addAttachmentFile(String attachmentFile) {
            this.attachmentFileList.add(attachmentFile);
        }
    }

}


