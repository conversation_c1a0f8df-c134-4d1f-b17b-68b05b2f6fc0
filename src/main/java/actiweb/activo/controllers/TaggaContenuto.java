package actiweb.activo.controllers;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Servizio per la gestione della logica di tagging delle fatture.
 * Opera in memoria senza interagire con il database.
 */
public class TaggaContenuto {

    // Mappa hardcoded che associa una parola chiave (in minuscolo) al nome del tag.
    // Puoi espandere questa mappa a piacimento.
    private static final Map<String, String> keywordToTagMap = new HashMap<>();

    static {
        keywordToTagMap.put("formattazione", "Hardware");
        keywordToTagMap.put("hard disk", "Hardware");
        keywordToTagMap.put("assistenza remota", "Hardware");
        keywordToTagMap.put("ripristino dati", "Hardware");
        keywordToTagMap.put("recupero dati", "Hardware");
        keywordToTagMap.put("virus", "Hardware");
        keywordToTagMap.put("kaspersky", "Hardware");

        keywordToTagMap.put("canone", "Canone");
        keywordToTagMap.put("periodica", "Canone");
        keywordToTagMap.put("rinnovo", "Canone");
        keywordToTagMap.put("licenza", "Canone");

        keywordToTagMap.put("cms", "Applicativo");
        keywordToTagMap.put("applicativo", "Applicativo");
        keywordToTagMap.put("applicativi", "Applicativo");
        keywordToTagMap.put("modulo", "Applicativo");
        keywordToTagMap.put("modulo aggiuntivo", "Applicativo");
        keywordToTagMap.put("moduli aggiuntivi", "Applicativo");

        keywordToTagMap.put("shop", "Ecommerce");
        keywordToTagMap.put("ecommerce", "Ecommerce");
        keywordToTagMap.put("e-commerce", "Ecommerce");

        keywordToTagMap.put("software", "Software");
        keywordToTagMap.put("gestionale", "Software");
        keywordToTagMap.put("xml attivi", "Software");
        keywordToTagMap.put("esportazione manuale", "Software");

        keywordToTagMap.put("pec", "Pec");
        keywordToTagMap.put("certificata", "Pec");

        keywordToTagMap.put("ssl", "Ssl");
        keywordToTagMap.put("certificato", "Ssl");

        keywordToTagMap.put("espansione", "Espansione");
        keywordToTagMap.put("upgrade", "Espansione");
        keywordToTagMap.put("ampliamento", "Espansione");
        keywordToTagMap.put("mail orarie", "Espansione");

        keywordToTagMap.put("hosting", "Hosting");
        keywordToTagMap.put("dominio", "Hosting");
        keywordToTagMap.put("domini", "Hosting");
        keywordToTagMap.put("pacchetto modifiche", "Hosting");
        keywordToTagMap.put("servizi internet", "Hosting");
        keywordToTagMap.put("server web", "Hosting");
        keywordToTagMap.put("vps", "Hosting");
        keywordToTagMap.put("gestione server", "Hosting");

        keywordToTagMap.put("mail aggiuntive", "Mail extra");
        keywordToTagMap.put("mail aggiuntiva", "Mail extra");
        keywordToTagMap.put("workspace", "Mail extra");
        keywordToTagMap.put("google workspace", "Mail extra");

        keywordToTagMap.put("storno", "Nota di credito");

        keywordToTagMap.put("modifiche varie", "Modifiche");
        keywordToTagMap.put("modifiche sito", "Modifiche");
        keywordToTagMap.put("modifiche extra", "Modifiche");
        keywordToTagMap.put("aggiunta", "Modifiche");
        keywordToTagMap.put("inserimento", "Modifiche");
        keywordToTagMap.put("restyling", "Modifiche");
        keywordToTagMap.put("fix", "Modifiche");

        // keywordToTagMap.put("modifiche", "Modifiche");
        keywordToTagMap.put("acconto", "Acconto");
        keywordToTagMap.put("saldo", "Saldo");
    }

    // Private constructor to prevent instantiation
    private TaggaContenuto() {
    }

    /**
     * Analizza un testo e restituisce un set di tag corrispondenti basati sulle
     * keyword.
     *
     * @param testo Il testo da analizzare (es. la descrizione di una fattura).
     * @return Un Set<String> contenente i tag trovati. L'uso di un Set previene
     *         duplicati.
     */
    public static Set<String> getTagsFromText(String testo) {
        Set<String> tagsTrovati = new HashSet<>();
        if (testo == null || testo.isBlank()) {
            return tagsTrovati;
        }
        String testoMinuscolo = testo.toLowerCase();
        // System.out.println("Testo in analisi: [" + testoMinuscolo + "]");

        keywordToTagMap.forEach((keyword, tag) -> {
            // Pattern pattern = Pattern.compile("\\b" + Pattern.quote(keyword) + "\\b");
            // boolean found = pattern.matcher(testoMinuscolo).find();
            boolean found = testoMinuscolo.contains(keyword);
            // System.out.println("Keyword: '" + keyword + "' -> Trovata? " + found);
            if (found) {
                tagsTrovati.add(tag);
            }
        });

        // --- NUOVA LOGICA PER SPECIFICITA' APPLICATIVO ---
        // Se è presente un tag più specifico come "Ecommerce", rimuovo il generico
        // "Applicativo".
        // Questo evita di avere sia "Canone Ecommerce" che "Canone Applicativo" per lo
        // stesso servizio.
        if (tagsTrovati.contains("Applicativo") && tagsTrovati.contains("Ecommerce")) {
            tagsTrovati.remove("Applicativo");
        }
        // --- FINE NUOVA LOGICA ---

        // --- NUOVA LOGICA DI COMBINAZIONE PER HOSTING ---
        // Se "Hosting" è presente con "Ssl", "Pec" o "Espansione", li combina.
        if (tagsTrovati.contains("Hosting")) {
            List<String> subTags = new ArrayList<>();
            if (tagsTrovati.contains("Ssl"))
                subTags.add("Ssl");
            if (tagsTrovati.contains("Pec"))
                subTags.add("Pec");
            if (tagsTrovati.contains("Espansione"))
                subTags.add("Espansione");

            if (!subTags.isEmpty()) {
                // Rimuovo i tag di base che verranno combinati
                tagsTrovati.remove("Hosting");
                tagsTrovati.removeAll(subTags);

                // Ordino i sub-tag per un output consistente (es. "Hosting + Pec + Ssl")
                Collections.sort(subTags);

                // Costruisco il nuovo tag combinato
                StringBuilder combinedTag = new StringBuilder("Hosting");
                for (String subTag : subTags) {
                    combinedTag.append(" + ").append(subTag);
                }

                // Aggiungo il nuovo tag combinato
                tagsTrovati.add(combinedTag.toString());
            }
        }
        // --- FINE NUOVA LOGICA ---

        // --- NUOVA LOGICA DI COMBINAZIONE TAG (Canone, Saldo, etc.) ---
        Set<String> combiners = new HashSet<>();
        if (tagsTrovati.contains("Canone"))
            combiners.add("Canone");
        if (tagsTrovati.contains("Saldo"))
            combiners.add("Saldo");
        if (tagsTrovati.contains("Acconto"))
            combiners.add("Acconto");
        // Aggiungere qui altri eventuali tag "combinatori"

        if (!combiners.isEmpty()) {
            Set<String> baseTags = new HashSet<>(tagsTrovati);
            baseTags.removeAll(combiners);

            // Se ci sono tag di base (es. "Hosting", "Applicativo") da combinare...
            if (!baseTags.isEmpty()) {
                Set<String> finalTags = new HashSet<>();
                for (String combiner : combiners) {
                    for (String baseTag : baseTags) {
                        finalTags.add(combiner + " " + baseTag);
                    }
                }
                // ...restituisci solo i tag combinati.
                return finalTags;
            }
            // Altrimenti, se ci sono solo tag combinatori (es. solo "Canone"),
            // la logica prosegue e restituisce i tagTrovati originali, che è corretto.
        }
        // --- FINE NUOVA LOGICA ---

        // System.out.println("Tag finali: " + tagsTrovati);
        return tagsTrovati;
    }

    /**
     * Analizza un testo di una fattura, estrae i blocchi di servizio con i relativi
     * prezzi,
     * li tagga e restituisce una lista di oggetti TaggedItem.
     *
     * @param testoFattura Il testo completo della fattura da analizzare.
     * @return Una List<TaggedItem> contenente un oggetto per ogni tag trovato in
     *         ogni blocco, con il prezzo del blocco.
     */
    public static List<TaggedItem> getTagsAndPricesFromText(String testoFattura) {
        List<TaggedItem> result = new ArrayList<>();
        if (testoFattura == null || testoFattura.isBlank()) {
            return result;
        }

        // Rimuove i calcoli di prezzo tra parentesi per non confondere il parser dei
        // blocchi.
        // Es: "(12x2.1€)" o "( 12 x 2.1 € )"
        String testoPulito = testoFattura.replaceAll("\\([^)]*€[^)]*\\)", "");

        // --- Logica di Normalizzazione ---
        // Sostituisce i caratteri "speciali" che possono interferire con il parsing.
        // 1. Sostituisce il non-breaking space (un carattere comune e problematico)
        // con uno spazio normale.
        testoPulito = testoPulito.replaceAll("\u00A0", " ");
        // 2. Sostituisce tutti i ritorni a capo (CR, LF) e i tab con un separatore
        // visibile (#). Questo "appiattisce" il testo in una singola linea logica,
        // rendendo il parsing successivo immune a differenze di formattazione.

        // testoPulito = testoPulito.replaceAll("[\\t\\r\\n]+", " # ");

        // Se il testo diventa vuoto dopo la pulizia, significa che conteneva solo
        // prezzi tra parentesi.
        // Questo è un caso ambiguo che etichettiamo come errore di parsing.
        if (testoPulito.isBlank()) {
            result.add(new TaggedItem("Parse Error", 0.0));
            return result;
        }

        // --- NUOVA Logica di Ricostruzione Blocchi basata su € ---
        // La logica precedente basata su newline era fragile e causava incoerenze.
        // Questa nuova logica usa il simbolo € come terminatore di un blocco di
        // servizio.
        // Il pattern ".*€" con DOTALL e non-greedy (reluctant) matching `*?`
        // cattura tutto il testo fino al prossimo simbolo €, inclusi i ritorni a capo.
        // Questo è robusto sia per testo su riga singola che multi-riga, garantendo
        // coerenza.
        List<String> blocchiRicostruiti = new ArrayList<>();
        Matcher blockMatcher = Pattern.compile(".*?€", Pattern.DOTALL).matcher(testoPulito);
        while (blockMatcher.find()) {
            String blocco = blockMatcher.group().trim();
            if (!blocco.isEmpty()) {
                blocchiRicostruiti.add(blocco);
            }
        }

        // Caso speciale: se il testo non contiene '€', il matcher non troverà nulla.
        // In questo caso, trattiamo l'intero testo come un unico blocco potenziale.
        // La logica successiva verificherà se contiene un prezzo valido.
        if (blocchiRicostruiti.isEmpty() && !testoPulito.trim().isEmpty()) {
            blocchiRicostruiti.add(testoPulito.trim());
        }

        // Se, dopo la ricostruzione, non abbiamo blocchi ma il testo originale non era
        // vuoto,
        // significa che non è stato trovato nessun prezzo. Lo segnaliamo come errore di
        // parsing.
        if (blocchiRicostruiti.isEmpty()) {
            result.add(new TaggedItem("Parse Error", 0.0));
            return result;
        }

        // Regex per estrarre un prezzo (un numero seguito da €). Es: "200.0€" ->
        // cattura "200.0"
        final Pattern pricePattern = Pattern.compile("([\\d,.]+)\\s*€");
        // Regex per estrarre il prezzo dalla riga del TOTALE. Es: "TOTALE 280.0€" ->
        // cattura "280.0"
        final Pattern totalPattern = Pattern.compile("TOTALE\\s+([\\d,.]+)\\s*€", Pattern.CASE_INSENSITIVE);

        for (String blocco : blocchiRicostruiti) {
            blocco = blocco.trim();
            if (blocco.isEmpty()) {
                continue;
            }

            double prezzo = 0.0;
            boolean priceFound = false;

            // Prima cerchiamo il pattern "TOTALE ... €" che è più specifico e affidabile.
            Matcher totalMatcher = totalPattern.matcher(blocco);
            if (totalMatcher.find()) {
                try {
                    String prezzoDaParsare = totalMatcher.group(1).replace(',', '.');
                    prezzo = Double.parseDouble(prezzoDaParsare);
                    priceFound = true;
                } catch (NumberFormatException e) {
                    System.err.println("Impossibile parsare il prezzo 'TOTALE' nel blocco: " + blocco);
                    result.add(new TaggedItem("Parse Error", 0.0));
                    continue; // Passa al blocco successivo
                }
            }

            // Se non troviamo "TOTALE", usiamo la vecchia logica come fallback:
            // cerchiamo l'ultimo prezzo nel blocco. Questo gestisce il caso che hai
            // sollevato.
            if (!priceFound) {
                Matcher priceMatcher = pricePattern.matcher(blocco);
                String ultimoPrezzoStr = null;
                while (priceMatcher.find()) {
                    ultimoPrezzoStr = priceMatcher.group(1);
                }

                if (ultimoPrezzoStr != null) {
                    try {
                        String prezzoDaParsare = ultimoPrezzoStr.replace(',', '.');
                        prezzo = Double.parseDouble(prezzoDaParsare);
                        priceFound = true;
                    } catch (NumberFormatException e) {
                        System.err.println("Impossibile parsare il prezzo (fallback) nel blocco: " + blocco);
                        result.add(new TaggedItem("Parse Error", 0.0));
                        continue; // Passa al blocco successivo
                    }
                }
            }

            if (!priceFound) {
                // Se un blocco è stato identificato (perché contiene '€') ma non riusciamo a
                // estrarre un prezzo valido, è un errore di parsing. Se il blocco non
                // contiene '€' (caso di fallback), lo ignoriamo.
                if (blocco.contains("€")) {
                    result.add(new TaggedItem("Parse Error", 0.0));
                }
                continue;
            }

            // Usiamo la funzione esistente per trovare i tag nel blocco di testo
            Set<String> tagsTrovati = getTagsFromText(blocco);

            if (tagsTrovati.isEmpty()) {
                System.out.println("\n***************\n Nessun tag trovato per il blocco con prezzo: " + prezzo);
                System.out.println("\n ++++++++Testo originale:" + testoPulito);
                System.out.println("\n ++++++++Blocchi ricostruiti:");
                for (String b : blocchiRicostruiti) {
                    System.out.println("  ->> " + b);
                }
                System.out.println("\n \n Blocco analizzato: \n---\n" + blocco + "\n---");
                // stampa i blocchi rinosciuti

                result.add(new TaggedItem("(Nessun Tag)", prezzo));
            } else {
                for (String tag : tagsTrovati) {
                    result.add(new TaggedItem(tag, prezzo));
                }
            }
        }
        return result;
    }

    public static void main(String[] args) {
        String testoFattura = """
                - Canone annuale applicativo: Licenza d'uso annuale moduli, aggiornamenti e patch di sicurezza bisettimanali/mensili, manutenzione e ottimizzazione periodica del database, assistenza utilizzo applicativo
                                                             TOTALE 490.0€ +IVA

                 - Canone annuale servizi tecnici con pacchetto modifiche:
                   Assistenza tecnica e configurazione assistita email, gestione politiche antispam personalizzata, mail alias e forward illimitati
                   Spazio email da 1 GB, spazio server web di 500 MB, traffico mensile 3 GB
                   Fino a 5 modifiche (non strutturali, non cumulabili) gratuite annuali dei contenuti del sito web
                   Gestione e rinnovo annuale N° 1 nome a dominio oliocoopmontalbano.it
                                                             TOTALE 190.0€ +IVA

                 - Ampliamento spazio mail + 2GB (da 1GB a 3GB) <EMAIL>
                                                             TOTALE 50.0€ +IVA  (2x25.0€)


                """;

        List<TaggedItem> items = getTagsAndPricesFromText(testoFattura);

        System.out.println("--- Singoli Tag e Prezzi ---");
        items.forEach(System.out::println);

        Map<String, Double> totaliPerTag = items.stream()
                .collect(Collectors.groupingBy(TaggedItem::getTag,
                        Collectors.summingDouble(TaggedItem::getPrice)));

        System.out.println("\n--- Totali per Tag ---");
        totaliPerTag.forEach(
                (tag, totale) -> System.out.println(tag + ": " +
                        String.format(Locale.ITALIAN, "%.2f", totale) + "€"));

    }

    /**
     * Classe interna per rappresentare l'associazione tra un tag e il suo prezzo.
     */
    public static class TaggedItem {
        private final String tag;
        private final double price;

        public TaggedItem(String tag, double price) {
            this.tag = tag;
            this.price = price;
        }

        public String getTag() {
            return tag;
        }

        public double getPrice() {
            return price;
        }

        @Override
        public String toString() {
            return "TaggedItem{" +
                    "tag='" + tag + '\'' +
                    ", price=" + price +
                    '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o)
                return true;
            if (o == null || getClass() != o.getClass())
                return false;
            TaggedItem that = (TaggedItem) o;
            return Double.compare(that.price, price) == 0 && Objects.equals(tag, that.tag);
        }

        @Override
        public int hashCode() {
            return Objects.hash(tag, price);
        }
    }
}