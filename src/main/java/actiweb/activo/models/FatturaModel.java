package actiweb.activo.models;

import actiweb.activo.controllers.Db;

import java.io.File;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Year;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import static actiweb.activo.controllers.Utils.resultSetToArrayList;

public class FatturaModel {
    /*  private static final String basePath = "src/main/java/actiweb/activo/assets/";*/
    String id;
    String idFattura;
    String idCliente;
    String ragioneSociale;
    Date dataFattura;
    String numFattura;
    String testoFattura;
    String XMLFattura;
    Integer annoFattura;
    Integer meseFattura;
    Integer tipoFattura; //2=nota di credito
    double prezzoTotaleFattura;
    double prezzoPagato;
    double prezzoImponibile;
    double importoIva;


    public static ArrayList<HashMap<String, Object>> caricaFatturatoPerMese(Year annoInizio) {
        if (annoInizio == null) {
            annoInizio = Year.of(2017);
        }
        ArrayList<HashMap<String, Object>> fatturatoPerMeseList = new ArrayList<>();
        //carica il fatturato dal db
        final String baseSQL = "SELECT CAST(YEAR(DataFattura) AS VARCHAR(4)) + '-' + right('00' + CAST(MONTH(DataFattura) AS VARCHAR(2)), 2) AS periodo,\n" +
                "ID,t1.Tipologia,Cliente,NumDoc,DataFattura,Imponibile,Iva,Totale,TotalePagato,t1.Note," +
                "YEAR(DataFattura) as AnnoFattura, MONTH(DataFattura) as MeseFattura, RagioneSociale \n" +
                "FROM ElencoScarico t1\n" +
                "INNER JOIN Clienti t2\n" +
                "ON t1.Cliente = t2.IdCliente\n" +
                "where Year(DataFattura)=%s\n" +
                "ORDER BY periodo ASC";

        final String SQL = String.format(baseSQL, annoInizio);
        try {
            PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL);
            ResultSet rs = prepStm.executeQuery();
            fatturatoPerMeseList = resultSetToArrayList(rs);
            return fatturatoPerMeseList;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }


    public Integer getMeseFattura() {
        return meseFattura;
    }

    public void setMeseFattura(Integer meseFattura) {
        this.meseFattura = meseFattura;
    }

    public double getImportoIva() {
        return importoIva;
    }

    public void setImportoIva(double importoIva) {
        this.importoIva = importoIva;
    }

    public String getRagioneSociale() {
        return ragioneSociale;
    }

    public void setRagioneSociale(String ragioneSociale) {
        this.ragioneSociale = ragioneSociale;
    }

    public Integer getTipoFattura() {
        return tipoFattura;
    }

    // [Pagato], [DataPagato]

    public void setTipoFattura(Integer tipoFattura) {
        this.tipoFattura = tipoFattura;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIdFattura() {
        return idFattura;
    }

    public void setIdFattura(String idFattura) {
        this.idFattura = idFattura;
    }

    public String getIdCliente() {
        return idCliente;
    }

    public void setIdCliente(String idCliente) {
        this.idCliente = idCliente;
    }

    public String getNumFattura() {
        return numFattura;
    }

    public void setNumFattura(String numFattura) {
        this.numFattura = numFattura;
    }

    public String getTestoFattura() {
        return testoFattura;
    }

    public void setTestoFattura(String testoFattura) {
        this.testoFattura = testoFattura;
    }

    public Date getDataFattura() {
        //System.out.println("getDataFattura " + dataFattura);
        return dataFattura;
    }

    public void setDataFattura(Date dataFattura) {
  /*      try {
            this.dataFattura = Utils.parseDate(dataFattura.toString());
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }*/

        //System.out.println("setDataFattura " + dataFattura);
        this.dataFattura = dataFattura;
    }

    public double getPrezzoTotaleFattura() {
/*        String stringNumber = String.valueOf(prezzoTotaleFattura);
        double ret=Double.parseDouble(stringNumber.replace(',','.'));
        System.out.println(prezzoTotaleFattura+"->"+ret);
        return ret;*/
        return prezzoTotaleFattura;
    }

    public void setPrezzoTotaleFattura(double prezzoTotaleFattura) {
        this.prezzoTotaleFattura = prezzoTotaleFattura;
    }

    public double getPrezzoPagato() {
        return prezzoPagato;
    }

    public void setPrezzoPagato(double prezzoPagato) {
        this.prezzoPagato = prezzoPagato;
    }

    public double getPrezzoImponibile() {
        return prezzoImponibile;
    }

    public void setPrezzoImponibile(double prezzoImponibile) {
        this.prezzoImponibile = prezzoImponibile;
    }

    public String getXMLFattura() {
        return XMLFattura;
    }

    public void setXMLFattura(String XMLFattura) {
        this.XMLFattura = XMLFattura;
    }

    public Integer getAnnoFattura() {
        return annoFattura;
    }

    public void setAnnoFattura(Integer annoFattura) {
        this.annoFattura = annoFattura;
    }

    public String buildFileName(String extension) {
        if (extension.isEmpty()) {
            extension = "pdf";
        }
        return "actiweb_ft-" + this.getNumFattura() + "-" + this.getAnnoFattura() + "." + extension;
    }

    public String buildFullFilenamePath(String basePath, String extension) {
        return buildFullFilenamePath(basePath, extension, false);
    }

    public String buildFullFilenamePath(String basePath, String extension, Boolean addFinalSlash) {
        //addFinalSlash = addFinalSlash == null ? false : addFinalSlash;
        System.out.println("File.separator=" + File.separator);
        String currentDir = System.getProperty("user.dir") + File.separator; //fixa il big della path su win?
        // String currentDir = System.getProperty("user.dir") + "/";
        if (basePath.isEmpty()) {
            //basePath = FatturaModel.basePath;
            basePath = "";
        } else {
            basePath = basePath + File.separator;
        }
        if (extension.isEmpty()) {
            extension = "pdf";
        }
        // System.out.println(currentDir + basePath + buildFileName(extension));
        if (addFinalSlash) {
            return currentDir + basePath + buildFileName(extension) + File.separator;
        } else {
            return currentDir + basePath + buildFileName(extension);
        }

        //return currentDir + basePath + "actiweb_ft-" + this.getNumFattura() + "-" + this.getAnnoFattura() + "." + extension;
    }
}
