package actiweb.activo.models;


import actiweb.activo.controllers.Db;

import javax.swing.*;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.EnumMap;

public class SollecitoModel {
    //  String idSollecito;
    public String idCliente;
    public String idSollecito;
    public ArrayList<String> listaIdFatture;
    public ArrayList<String> listaFatture;
    public Date dataSollecito;
    public String testoSollecito;
    public ArrayList<String> listaMailDestinatario;
    private String ragioneSociale;
    private TipoDiEmail tipoDiComunicazione;

    public SollecitoModel() {

    }

    public SollecitoModel(String idCliente, ArrayList<String> listaIdFatture, ArrayList<String> listaFatture, String testoSollecito, String ragioneSociale) {

        // this.idSollecito = UUID.randomUUID().toString();
        this.idCliente = idCliente;
        this.listaIdFatture = listaIdFatture;
        this.listaFatture = listaFatture;
        this.dataSollecito = new Date();
        this.testoSollecito = testoSollecito;
        this.setRagioneSociale(ragioneSociale);
        this.listaMailDestinatario = new ArrayList<>();
        this.tipoDiComunicazione = TipoDiEmail.SOLLECITO;
    }

    public static ArrayList<SollecitoModel> caricaSollecitiDalDb(String idCliente) {

        String where = "";
        if (!(idCliente == null) && !(idCliente.isBlank())) {
            where = " WHERE idCliente=" + idCliente + " ";
        }
        ArrayList<SollecitoModel> listaSolleciti = new ArrayList<SollecitoModel>();
        final String SQL = "select * from Solleciti " + where + " ORDER BY dataSollecito DESC";

        try {
            PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL);
            ResultSet rs = prepStm.executeQuery();
            while (rs.next()) {
                SollecitoModel loadedSol = new SollecitoModel();
                loadedSol.idSollecito = rs.getString("idSollecito");
                loadedSol.idCliente = rs.getString("idCliente");
                loadedSol.dataSollecito = rs.getDate("dataSollecito");
                loadedSol.testoSollecito = rs.getString("testoSollecito");
                loadedSol.setRagioneSociale(rs.getString("ragioneSociale"));

                loadedSol.setTipoDiComunicazione(TipoDiEmail.SOLLECITO);
                String tipoComunicaz = rs.getString("tipoDiComunicazione");
                if ((tipoComunicaz != null) && (!tipoComunicaz.isBlank())) {
                    loadedSol.setTipoDiComunicazione(TipoDiEmail.valueOf(tipoComunicaz));
                }


                ArrayList<String> stringList = new ArrayList<>();
                String[] stringArray = rs.getString("listaFatture").split(",");
                Collections.addAll(stringList, stringArray);
                loadedSol.listaFatture = stringList;

                ArrayList<String> stringList2 = new ArrayList<>();
                String[] stringArray2 = rs.getString("listaIdFatture").split(",");
                Collections.addAll(stringList2, stringArray2);
                loadedSol.listaIdFatture = stringList2;

                ArrayList<String> stringList3 = new ArrayList<>();
                String[] stringArray3 = rs.getString("listaMailDestinatario").split(",");
                Collections.addAll(stringList3, stringArray3);
                loadedSol.listaMailDestinatario = stringList3;

                listaSolleciti.add(loadedSol);

                //System.out.println("\n" + loadedSol.ragioneSociale + " \n" + loadedSol.testoSollecito);
            }
            return listaSolleciti;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }


    }

    public static String descrTipoComunicazione(TipoDiEmail tipo) {
        EnumMap<TipoDiEmail, String> tipoComunicazioneMap = new EnumMap<>(TipoDiEmail.class);
        tipoComunicazioneMap.put(TipoDiEmail.SOLLECITO, "Sollecito fatture non pagate");
        tipoComunicazioneMap.put(TipoDiEmail.CORTESIA, "Fattura di cortesia in PDF");
        return tipoComunicazioneMap.get(tipo);
    }

    public static int eliminaComunicazioneDalDb(String idSollecito) {

        if ((idSollecito == null) || (idSollecito.isBlank())) {
            System.out.println("Nessun sollecito selezionato");
            return 0;
        }
        //final String SQL = "DELETE from Solleciti WHERE idSollecito=" + idSollecito;

        try {
            String SQL = "DELETE FROM Solleciti WHERE idSollecito = ?";
            PreparedStatement statement = Db.getDbConn().prepareStatement(SQL);
            statement.setInt(1, Integer.parseInt(idSollecito));
            int rowsDeleted = statement.executeUpdate();


            if (rowsDeleted > 0) {
                System.out.println("Riga con id " + idSollecito + " eliminata correttamente.");
                JOptionPane.showMessageDialog(null, "Riga con id " + idSollecito + " eliminata correttamente.");

                return 1;
            } else {
                System.out.println("Riga con id " + idSollecito + " non trovata nella tabella.");
                JOptionPane.showMessageDialog(null, "Riga con id " + idSollecito + " non trovata nella tabella.");
                return 0;
            }

        } catch (SQLException e) {
            e.printStackTrace();
        }
        return 0;
    }

    public String descrTipoComunicazioneCorrente() {
        return descrTipoComunicazione(tipoDiComunicazione);
    }

    public TipoDiEmail getTipoDiComunicazione() {
        return tipoDiComunicazione;
    }

    public void setTipoDiComunicazione(TipoDiEmail tipoDiComunicazione) {
        this.tipoDiComunicazione = tipoDiComunicazione;
    }

    public String getRagioneSociale() {
        return ragioneSociale;
    }

    public void setRagioneSociale(String ragioneSociale) {
        String senzaApiciSingoli = ragioneSociale.replace("'", "");
        // Rimuovi gli apici doppi
        String senzaApiciDoppi = senzaApiciSingoli.replace("\"", "");
        this.ragioneSociale = senzaApiciDoppi;
    }

    @Override
    public String toString() {
        return "SollecitoModel{" +
                //  "idSollecito='" + idSollecito + '\'' +
                ", idCliente='" + idCliente + '\'' +
                ", listaIdFatture=" + listaIdFatture +
                ", listaFatture=" + listaFatture +
                ", dataSollecito=" + dataSollecito +
                ", testoSollecito='" + testoSollecito + '\'' +
                ", ragioneSociale='" + ragioneSociale + '\'' +
                //  ", tableName='" + tableName + '\'' +
                '}';
    }

    public String getListaIdFattureString() {

        if ((!(listaIdFatture == null)) && (!listaIdFatture.isEmpty())) {
            return String.join(",", listaIdFatture);
        }
        return "";
    }

    public String getListaFattureString() {
        if ((!(listaFatture == null)) && (!listaFatture.isEmpty())) {
            return String.join(",", listaFatture);
        }
        return "";

    }

    public String getListaMailDestinatarioString() {
        if ((!(listaMailDestinatario == null)) && (!listaMailDestinatario.isEmpty())) {
            return String.join(",", listaMailDestinatario);
        }
        return "";
    }

    public int salvaSollecitoNelDb() {

/*        UPDATE Customers
        SET ContactName='Juan'
        WHERE Country='Mexico';*/
        int result;

        LocalDate data = LocalDate.now();
        //DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd"); //deve essere yyyyMMdd sennò non lo salva
        String dataFormattata = data.format(formatter);
        System.out.println(dataFormattata);

/*        Locale locale = new Locale("it", "IT");
        DateFormat dateFormat = DateFormat.getDateInstance(DateFormat.DEFAULT, locale);
        String dataFormattata = dateFormat.format(new Date());*/

        final String campi = "(idCliente, listaIdFatture, listaFatture, dataSollecito, testoSollecito, ragioneSociale, listaMailDestinatario, tipoDiComunicazione)";

        final String baseSQL = "INSERT INTO Solleciti " + campi + " VALUES(%s, '%s', '%s', '%s', '%s', '%s', '%s', '%s');";
        final String SQL = String.format(baseSQL,
                this.idCliente,
                this.getListaIdFattureString(),
                this.getListaFattureString(),
                dataFormattata,
                this.testoSollecito,
                this.ragioneSociale,
                this.getListaMailDestinatarioString(),
                this.getTipoDiComunicazione()
        );

        try {
            PreparedStatement prepStm = Db.getDbConn().prepareStatement(SQL);
            result = prepStm.executeUpdate();
            System.out.println("affected row " + result);
            return result;

        } catch (SQLException e) {
            throw new RuntimeException(e);
        }

    }


    public enum TipoDiEmail {
        SOLLECITO,
        CORTESIA,

    }
}

