package actiweb.activo.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.Date;

public class DateComparator implements Comparator<Object> {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("dd/MM/yyyy");

    @Override
    public int compare(Object o1, Object o2) {
        System.out.println(o1.toString() + "->" + o2.toString());
        try {
            Date date1 = DATE_FORMAT.parse((String) o1);
            Date date2 = DATE_FORMAT.parse((String) o2);
            return date2.compareTo(date1);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }
}
