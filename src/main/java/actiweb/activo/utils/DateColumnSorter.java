package actiweb.activo.utils;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Vector;

public class DateColumnSorter {

    public static void enableSortingByDateColumn(JTable table, int columnIndex) {
        table.getTableHeader().addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (table.getColumnModel().getColumnIndexAtX(e.getX()) == columnIndex) {
                    sortTableByDateColumn(table, columnIndex);
                }
            }
        });
    }

    public static void sortTableByDateColumn(JTable table, int columnIndex) {
        DefaultTableModel model = (DefaultTableModel) table.getModel();
        Object[] data = model.getDataVector().toArray();

        Arrays.sort(data, new Comparator<Object>() {
            final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");


            @Override
            public int compare(Object o1, Object o2) {
                try {
                    Object dateObj1 = ((Vector<?>) o1).get(columnIndex);
                    Object dateObj2 = ((Vector<?>) o2).get(columnIndex);

                    if (dateObj1 == null || dateObj2 == null) {
                        return 0;
                    }

                    String dateStr1 = dateObj1.toString();
                    String dateStr2 = dateObj2.toString();

                    if (dateStr1.isEmpty() || dateStr2.isEmpty()) {
                        return 0;
                    }

                    return dateFormat.parse(dateStr1).compareTo(dateFormat.parse(dateStr2));
                } catch (ParseException ex) {
                    ex.printStackTrace();
                    return 0;
                }
            }
        });

        for (int i = 0; i < data.length; i++) {
            model.removeRow(0);
            model.addRow(((Vector<?>) data[i]).toArray());
        }
    }
}