<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.actiweb</groupId>
    <artifactId>Activo</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>20</maven.compiler.source>
        <maven.compiler.target>20</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <version>12.2.0.jre11</version>
        </dependency>
        <!--        <dependency>
                    <groupId>com.owlike</groupId>
                    <artifactId>genson</artifactId>
                    <version>1.6</version>
                </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.sun.mail/javax.mail -->
        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>
        <!--
                &lt;!&ndash; https://mvnrepository.com/artifact/org.apache.xmlgraphics/fop &ndash;&gt;
                <dependency>
                    <groupId>org.apache.xmlgraphics</groupId>
                    <artifactId>fop</artifactId>
                    <version>2.9</version>
                </dependency>-->

        <!-- https://mvnrepository.com/artifact/xalan/xalan -->
        <!--        <dependency>
                    <groupId>xalan</groupId>
                    <artifactId>xalan</artifactId>
                    <version>2.7.3</version>
                </dependency>-->
        <!-- https://mvnrepository.com/artifact/org.apache.ws.commons.util/ws-commons-util -->
        <!--        <dependency>
                    <groupId>org.apache.ws.commons.util</groupId>
                    <artifactId>ws-commons-util</artifactId>
                    <version>1.0.2</version>
                </dependency>-->

        <!--        <dependency>
                    <groupId>xml-apis</groupId>
                    <artifactId>xml-apis</artifactId>
                    <version>1.4.01</version>
                </dependency>-->

        <!-- Dipendenza per la conversione XML in HTML -->
        <!-- https://mvnrepository.com/artifact/net.sf.saxon/saxon -->
        <dependency>
            <groupId>net.sf.saxon</groupId>
            <artifactId>saxon</artifactId>
            <version>8.7</version>
        </dependency>


        <!-- Dipendenza per la conversione HTML in PDF -->
        <!-- https://mvnrepository.com/artifact/com.itextpdf/itextpdf -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>********</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.itextpdf.tool/xmlworker -->
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>********</version>
        </dependency>

    </dependencies>


</project>